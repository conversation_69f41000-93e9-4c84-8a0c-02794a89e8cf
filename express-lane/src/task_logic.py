from prefect import get_run_logger, task
from utils.tasks.affiliates.helper import AffiliateTasksHelper


class TaskLogic:
    def __init__(self, global_variables):
        raw_data = global_variables.get("raw_data")
        self.task_config = global_variables.get("task_config")
        self.config_rules = global_variables.get("config_rules")
        self.affiliate_assambler = global_variables.get("affiliate_assambler")

        self.helper = AffiliateTasksHelper(raw_data)

    @task(name="skip_content_logic")
    def skip_content(self) -> None:
        logger = get_run_logger()
        logger.info("STEP 1: Skip Content")
        try:
            for affiliates, values in self.task_config.get("affiliates").items():

                applied_rules = values.get("applied_rules", {})
                skip_affiliate_rule = applied_rules.get("skip_affiliate_rule")
                delivery_name = values.get("delivery_name")

                if skip_affiliate_rule:
                    mvpd_list = (
                        self.affiliate_assambler.get("clients", {})
                        .get("rules", {})
                        .get("mvpd", [])
                    )

                    for matched_affiliate in mvpd_list[:]:
                        if (
                            matched_affiliate.get("@ascp_client") == delivery_name
                            or matched_affiliate.get("@workgroup_name") == delivery_name
                        ):

                            if skip_affiliate_rule:
                                logger.debug(
                                    f"The given affiliate will be skipped from current process networkname -> {affiliates}"
                                )
                                mvpd_list.remove(matched_affiliate)

        except Exception as e:
            logger.error(f"STEP 1: Error on Skip content: {e}")
            raise

    @task(name="apply_category_rules_logic")
    def apply_category_rules(self) -> None:
        # STEP 2: Apply Category Rules
        logger = get_run_logger()
        logger.info("STEP 2: Apply Category Rules")
        try:
            for _, values in self.task_config.get("affiliates", {}).items():

                mvpd_list = (
                    self.affiliate_assambler.get("clients", {})
                    .get("rules", {})
                    .get("mvpd", [])
                )

                delivery_name = values.get("delivery_name")
                applied_rules = values.get("applied_rules", {})

                category = applied_rules.get("split_rules", None)
                movie = applied_rules.get("apply_category_rules", {}).get("movie")
                season = applied_rules.get("apply_category_rules", {}).get("season")
                none_category = applied_rules.get("apply_category_rules", {}).get(
                    "category"
                )

                if category is None or category == []:
                    logger.info(
                        f"No category to apply on data for Affiliate: {delivery_name}"
                    )
                    continue  # This skips to the next affiliate

                for matched_affiliate in mvpd_list[:]:
                    if (
                        matched_affiliate.get("@ascp_client") == delivery_name
                        or matched_affiliate.get("@workgroup_name") == delivery_name
                    ):
                        config_rules = self.config_rules.get("rules_category")
                        path_split = config_rules.get("path_split")
                        key_split = config_rules.get("key_split")
                        value_split = config_rules.get("value_split")

                        if none_category is None and None in movie and None in season:
                            logger.info(
                                f"Skipping affiliate, no category/movie/season data, affiliate will be removed: {delivery_name}"
                            )
                            mvpd_list.remove(matched_affiliate)

                        else:
                            app_data_list = self.helper.glom_helper().glom_obj(
                                matched_affiliate, path_split
                            )

                            # Remove existing Category entries
                            existing_indexes = [
                                index
                                for index, item in enumerate(app_data_list)
                                if item.get(key_split) == value_split
                            ]
                            for i in sorted(existing_indexes, reverse=True):
                                self.helper.glom_helper().glom_delete_obj(
                                    data=matched_affiliate, path=path_split, index=i
                                )

                            # Add new category values
                            if category:
                                for _category in category:
                                    app_data_list.append(_category)

                            logger.info(
                                f"For gvien affiliate: {delivery_name} apply the next category/(es): {category}"
                            )

        except Exception as e:
            logger.error(f"STEP 2: Error on Apply Category Rules: {e}")
            raise

    @task(name="removed_attributes_logic")
    def removed_attributes(self) -> None:
        logger = get_run_logger()
        logger.info("Step 3: Removed Attributes Rules")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                removed_attributes = values.get("remove_attributes")

                if removed_attributes:

                    keys = removed_attributes.get("keys")
                    path = removed_attributes.get("path")
                    filter_key = removed_attributes.get("filter_key")

                    for matched_affiliate in (
                        self.affiliate_assambler.get("clients", {})
                        .get("rules", {})
                        .get("mvpd", [])
                    ):

                        if (
                            matched_affiliate.get("@ascp_client") == delivery_name
                            or matched_affiliate.get("@workgroup_name") == delivery_name
                        ):

                            for k in keys:
                                app_data_list = self.helper.glom_helper().glom_obj(
                                    matched_affiliate, path
                                )

                                title_index = next(
                                    (
                                        index
                                        for index, item in enumerate(app_data_list)
                                        if item.get(filter_key) == k
                                    ),
                                    None,
                                )

                                # If found, update the value
                                if title_index is not None:
                                    self.helper.glom_helper().glom_delete_obj(
                                        data=matched_affiliate,
                                        path=path,
                                        index=title_index,
                                    )
                                    logger.info(
                                        f"Removed Attributes for given affiliate: {delivery_name} Attributes Removed: {path} --> {keys}"
                                    )
        except Exception as e:
            logger.error(f"Step 3: ERROR -> Removed Attributes Rules: {e}")
            raise

    @task(name="update_attributes_logic")
    def update_attributes(self) -> None:
        logger = get_run_logger()
        logger.info("Step 4: Update Attributes Logic")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                update_attributes = values.get("update_attributes")

                if update_attributes:
                    for update_attribute in update_attributes:

                        value_filter = update_attribute.get("value_filter")
                        key_filter = update_attribute.get("key_value_filter")
                        value_update = update_attribute.get("value_update")

                        for matched_affiliate in (
                            self.affiliate_assambler.get("clients", {})
                            .get("rules", {})
                            .get("mvpd", [])
                        ):

                            flatten_rules = self.helper.flatten_rules_helper(
                                matched_affiliate
                            )

                            if (
                                matched_affiliate.get("@ascp_client") == delivery_name
                                or matched_affiliate.get("@workgroup_name")
                                == delivery_name
                            ):
                                filter_sibling_tuples_by_value = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )
                                extract_single_path_by_key = (
                                    flatten_rules.extract_tuple_value(
                                        filter_sibling_tuples_by_value, key_filter
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        filter_sibling_tuples_by_value,
                                        f"{extract_single_path_by_key}{value_update}",
                                        key_filter,
                                    )
                                )
                                extract_specific_tuple = (
                                    flatten_rules.extract_specific_tuple(
                                        replace_tuples_value, key_filter
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, extract_specific_tuple
                                )
                                logger.info(
                                    f"Update Attributes for given affiliate: {delivery_name} Attributes Updated: {extract_specific_tuple}"
                                )

        except Exception as e:
            logger.error(f"Step 4: ERROR -> Update Attributes Logic: {e}")
            raise

    @task(name="replace_attributes_logic")
    def replace_attributes(self) -> None:
        logger = get_run_logger()
        logger.info("Step 5: Replace Attributes Logic")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                replace_attributes = values.get("replace_attributes")

                if replace_attributes:
                    for replace_attribute in replace_attributes:

                        key_value_filter = replace_attribute.get("key_value_filter")
                        value_filter = replace_attribute.get("value_filter")
                        filter_by = replace_attribute.get("filter_by")

                        for matched_affiliate in (
                            self.affiliate_assambler.get("clients", {})
                            .get("rules", {})
                            .get("mvpd", [])
                        ):

                            flatten_rules = self.helper.flatten_rules_helper(
                                matched_affiliate
                            )

                            flatten_raw_data_rules = (
                                self.helper.flatten_raw_data_rules_helper()
                            )

                            if (
                                matched_affiliate.get("@ascp_client") == delivery_name
                                or matched_affiliate.get("@workgroup_name")
                                == delivery_name
                            ):

                                by_key_value_filter = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                by_value_filter = (
                                    flatten_rules.filter_single_path_by_key(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_key_value_filter, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        by_value_filter,
                                        extract_single_path_by_key,
                                        value_filter,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger.info(
                                    f"Update Attributes for given affiliate:{delivery_name} Attributes Updated:{replace_tuples_value}"
                                )

                                by_value = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                filter_single_path_by_value = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_value, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        filter_single_path_by_value,
                                        extract_single_path_by_key,
                                        filter_by,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger.info(
                                    f"Update Attributes for given affiliate: {delivery_name} Attributes Updated: {replace_tuples_value}"
                                )

        except Exception as e:
            logger.error(f"Step 5: ERROR -> Replace Attributes Logic: {e}")
            raise

    @task(name="replace_attributes_by_logic")
    def replace_attributes_by(self) -> None:
        logger = get_run_logger()
        logger.info("Step 5: Replace Attributes By Logic")

        try:
            for _, values in self.task_config.get("affiliates").items():

                delivery_name = values.get("delivery_name")
                replace_attributes_by = values.get("replace_attributes_by")

                if replace_attributes_by:
                    for replace_attribute in replace_attributes_by:

                        key_value_filter = replace_attribute.get("key_value_filter")
                        value_filter = replace_attribute.get("value_filter")
                        filter_by = replace_attribute.get("filter_by")

                        for matched_affiliate in (
                            self.affiliate_assambler.get("clients", {})
                            .get("rules", {})
                            .get("mvpd", [])
                        ):

                            flatten_rules = self.helper.flatten_rules_helper(
                                matched_affiliate
                            )

                            flatten_raw_data_rules = (
                                self.helper.flatten_raw_data_rules_helper()
                            )

                            if (
                                matched_affiliate.get("@ascp_client") == delivery_name
                                or matched_affiliate.get("@workgroup_name")
                                == delivery_name
                            ):

                                by_key_value_filter = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                by_value_filter = (
                                    flatten_rules.filter_single_path_by_key(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_key_value_filter, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        by_value_filter,
                                        extract_single_path_by_key,
                                        value_filter,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger.info(
                                    f"Update Attributes for given affiliate:{delivery_name} Attributes Updated:{replace_tuples_value}"
                                )

                                by_value = flatten_raw_data_rules.filter_sibling_tuples_by_value(
                                    key_value_filter
                                )
                                filter_single_path_by_value = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )

                                extract_single_path_by_key = (
                                    flatten_raw_data_rules.extract_tuple_value(
                                        by_value, "value"
                                    )
                                )
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        filter_single_path_by_value,
                                        extract_single_path_by_key,
                                        filter_by,
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )

                                logger.info(
                                    f"Update Attributes for given affiliate: {delivery_name} Attributes Updated: {replace_tuples_value}"
                                )

        except Exception as e:
            logger.error(f"Step 5: ERROR -> Replace Attributes Logic: {e}")
            raise

    @task(name="limit_length_logic")
    def limit_length(self) -> None:
        logger = get_run_logger()
        logger.info("Step 6: limit Length")

        try:
            for _, values in self.task_config.get("affiliates").items():

                limit_length = values.get("limit_length", {})
                value_filter = limit_length.get("value_filter")
                key_filter = limit_length.get("key_filter")
                limit_length_validation = limit_length.get("validation")
                delivery_name = values.get("delivery_name")

                if limit_length:

                    for matched_affiliate in (
                        self.affiliate_assambler.get("clients", {})
                        .get("rules", {})
                        .get("mvpd", [])
                    ):

                        flatten_rules = self.helper.flatten_rules_helper(
                            matched_affiliate
                        )

                        if (
                            matched_affiliate.get("@ascp_client") == delivery_name
                            or matched_affiliate.get("@workgroup_name") == delivery_name
                        ):

                            if limit_length_validation:

                                length = limit_length.get("length")

                                search_by = (
                                    flatten_rules.filter_sibling_tuples_by_value(
                                        value_filter
                                    )
                                )

                                extract_value = flatten_rules.extract_tuple_value(
                                    search_by, key_filter
                                )
                                extract_specific_tuple = (
                                    flatten_rules.extract_specific_tuple(
                                        search_by, key_filter
                                    )
                                )

                                value_limit_length = extract_value[: int(length)]
                                replace_tuples_value = (
                                    flatten_rules.replace_tuples_value(
                                        extract_specific_tuple, value_limit_length
                                    )
                                )
                                flatten_rules.update_obj(
                                    matched_affiliate, replace_tuples_value
                                )
                                logger.info(
                                    f"Limit Lnegth Attribute for given affiliate: {delivery_name} Attributes: {replace_tuples_value}"
                                )

        except Exception as e:
            logger.error(f"Step 6: ERROR -> limit Length: {e}")
            raise
