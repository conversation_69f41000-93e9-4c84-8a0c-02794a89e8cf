import copy
import tempfile

from lxml import etree
from prefect import get_run_logger, task
from utils.extract import extract
from utils.load import load

from .task_logic import TaskLogic


class TaskAffiliateAssembler:
    def __init__(self, global_variables: dict):
        self.raw_data = global_variables.get("raw_data")
        self.secrets = global_variables.get("secrets_manager")
        self.task_config = global_variables.get("task_config")
        self.adi_data = copy.deepcopy(self.raw_data.get("master", {}).get("ADI"))

    @task(name="assemble")
    def assemble(self) -> dict:
        logger = get_run_logger()
        logger.info("Assamble Affilaite object Starter")
        obj = {"mvpd": []}

        for _, values in self.task_config.get("affiliates", {}).items():
            delivery_name = values.get("delivery_name")
            faspex = values.get("faspex")
            tar = values.get("tar")

            if faspex:
                logger.info("Adding Faspex mvpd: %s", delivery_name)
                obj["mvpd"].append(
                    {
                        "@workgroup_name": delivery_name,
                        "ADI": copy.deepcopy(self.adi_data),
                    }
                )
                logger.info("Adding workgroup_name mvpd: %s", delivery_name)
            else:
                mvpd_block = {
                    "@ascp_client": delivery_name,
                    "ADI": copy.deepcopy(self.adi_data),
                    "ascp": self.secrets.get(delivery_name),
                }
                logger.info("Adding ascp_client mvpd: %s", delivery_name)
                if tar:
                    mvpd_block["changes"] = {"format": "TAR"}
                    logger.info("Adding TAR mvpd: %s", delivery_name)
                else:
                    logger.info("Adding ASCP mvpd: %s", delivery_name)
                obj["mvpd"].append(mvpd_block)

        return {"clients": {"rules": obj}}


class TaskExtractLoad:
    @task(name="extract_obj")
    def extract_obj(self, file_name_from_s3, landing_bucket) -> dict:
        logger = get_run_logger()
        logger.info("Running Task: Extract Obj")

        task_def = {}

        task_def["extract"] = {
            "task": "s3_extract",
            "params": {
                "key": file_name_from_s3,
                "bucket": landing_bucket,
            },
        }
        prev = extract(task_def)

        return prev

    @task(name="load_obj")
    def load_obj(
        self, key, body, destination_bucket, output_filename, timezone
    ) -> dict:
        logger = get_run_logger()
        logger.info("Running Task: Load Obj")

        task_def = {}

        task_def["load"] = {
            "task": "s3_upload",
            "params": {
                "Key": key,
                "Bucket": destination_bucket,
                "Body": body,
                "filename_pattern": output_filename,
                "timezone": timezone,
            },
        }

        load(task_def)

        return {"message": "load susccessfull"}

    def extract_local_obj(self, read_local_file_path: str) -> etree._Element:
        logger = get_run_logger()
        logger.info("Running Task: Extract Local XML Obj")

        try:
            with open(read_local_file_path, "r") as f:
                root = f.read()
                return root
        except Exception as e:
            logger.error(f"Error loading or parsing local XML file: {e}")
            raise

    def load_local_obj(
        self, load_local_file_path: str, xml: str, tmp: bool = False
    ) -> None:
        logger = get_run_logger()

        logger.info("Running Task: Load Local Obj")

        try:
            with open(load_local_file_path, "w", encoding="utf-8") as f:
                f.write(xml)
        except Exception as e:
            logger.error(f"Failed to write XML to file: {e}")
            raise


class Tasks:
    def __init__(self, global_variables) -> None:
        self.task_logic = TaskLogic(global_variables)

    @task(name="logic_tasks")
    def logic_tasks(self) -> None:
        logger = get_run_logger()
        logger.info("Running Main Task")

        # STEP 1: Filter by Skip content
        self.task_logic.skip_content()

        # STEP 2: Apply Category Rules
        self.task_logic.apply_category_rules()

        # Step 3: Removed Attributes Rules
        self.task_logic.removed_attributes()

        # Step 4: UPDATE ATTRIBUTES LOGIC
        self.task_logic.update_attributes()

        # Step 5: REPLACE ATTRIBUTES LOGIC
        self.task_logic.replace_attributes()

        # Step 5: REPLACE ATTRIBUTES BY LOGIC
        self.task_logic.replace_attributes_by()

        # Step 6: Limit length
        self.task_logic.limit_length()
