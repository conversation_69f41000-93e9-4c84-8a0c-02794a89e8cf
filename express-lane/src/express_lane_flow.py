from collections import OrderedDict
from copy import deepcopy
from datetime import datetime

import xm<PERSON>od<PERSON>
from prefect import flow, get_run_logger
from utils.config_settings.express_lane_config_variables import config_variables
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.status_tracker import ProcessStatusTracker
from utils.tasks.data_processing import convert_from_dict

from .task_config import task_config_assembler
from .tasks import TaskAffiliateAssembler, TaskExtractLoad, Tasks


@flow(flow_run_name=f"express-lane-flow-{datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}")
def express_lane_flow(event: dict, local: bool = False) -> None:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """
    logger = get_run_logger()
    logger.info(
        f"************ IZMA / Running Lambda Express Lane ************: {event}"
    )
    try:
        global_variables = config_variables(event=event)

        event_settings_rules = global_variables.get("event_settings_rules")
        landing_bucket = global_variables.get("landing_bucket")
        destination_bucket = global_variables.get("destination_bucket")
        output_filename = global_variables.get("output_filename")
        timezone = global_variables.get("timezone")
        file_name_from_s3 = global_variables.get("file_name_from_s3")
        status_table_name = global_variables.get("status_table_name")
        event_project_name = global_variables.get("event_project_name", None)

        status_tracker = StatusTrackerSchema(
            client_id=file_name_from_s3, landing_bucket=landing_bucket
        )
        process_status_tracker = ProcessStatusTracker(
            status_table_name=status_table_name
        )

        process_status_tracker.create_initial_status(create_item=status_tracker)

        extract_task_load = TaskExtractLoad()

        if local:
            # Task Load Object on Local
            read_local_file_path = event.get("test", {}).get("read_local_file_path")
            prev = extract_task_load.extract_local_obj(read_local_file_path)

        else:
            # Task Load Object on Cloud
            prev = extract_task_load.extract_obj(file_name_from_s3, landing_bucket)

        _raw_data = xmltodict.parse(prev, dict_constructor=OrderedDict)

        raw_data = deepcopy(_raw_data)

        affiliates = (
            raw_data.get("master", {}).get("affiliate_list", {}).get("affiliate", {})
        )

        # Ensure it's always a list
        if not isinstance(affiliates, list):
            affiliates = [affiliates]

        filtered_affiliates = {
            k: event_settings_rules[k] for k in affiliates if k in event_settings_rules
        }

        global_variables["raw_data"] = raw_data

        task_config_assambler = task_config_assembler(
            global_variables=global_variables, filtered_affiliates=filtered_affiliates
        )

        global_variables["task_config"] = task_config_assambler

        affiliate_assambler = TaskAffiliateAssembler(
            global_variables=global_variables
        ).assemble()

        global_variables["affiliate_assambler"] = affiliate_assambler

        tasks = Tasks(global_variables)

        process_status_tracker.mark_as_running(client_id=file_name_from_s3)

        # Main Task
        tasks.logic_tasks()

        process_status_tracker.mark_as_completed(client_id=file_name_from_s3)

        prev = convert_from_dict(affiliate_assambler, "xml")

        file_name = file_name_from_s3[:-4]
        file_key = f"{file_name}_delivery.xml"
        file_obj = f"results/{file_key}"

        if local:
            load_local_file_path = event.get("test", {}).get("load_local_file_path")

            extract_task_load.load_local_obj(
                load_local_file_path=load_local_file_path,
                xml=prev,
            )

        else:
            # Task Load Object
            extract_task_load.load_obj(
                key=file_obj,
                body=prev,
                destination_bucket=destination_bucket,
                output_filename=output_filename,
                timezone=timezone,
            )

        logger.info("IZMA / Lambda Express Lane finished")

    except BaseException as error:
        error_message = f"{error}"
        logger.error(
            "IZMA ERROR: %s -- event settings: %s",
            error_message,
            event_project_name,
        )
        process_status_tracker.mark_as_failed(
            client_id=file_name_from_s3, error_message=error_message
        )


if __name__ == "__main__":
    base_name = "CrashCourseCuisinewi_S01E02_D4_10986638_HD_delivery"
    date_format = datetime.now().strftime("%y_%m_%d")
    express_lane_flow(
        event={
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
                        },
                        "bucket": {"name": "express-lane-test"},
                    },
                    "prefect_args": {
                        "flow_id": "0817abd8-e6fa-4408-83ae-c945b13c0238",
                        "deployment_id": "001920cc-c744-423a-ab25-f7692d6d6e5e",
                    },
                }
            ],
            "test": {
                "read_local_file_path": "/Users/<USER>/Library/CloudStorage/OneDrive-Globant/disnay/projects/tacdev-projects/express-lane/tests/affiliates/files/input/CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
                "load_local_file_path": "/Users/<USER>/Library/CloudStorage/OneDrive-Globant/disnay/projects/tacdev-projects/express-lane/tests/affiliates/files/output/express_lane",
            },
        },
        local=True,
    )
