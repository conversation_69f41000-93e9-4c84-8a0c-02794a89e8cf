{"Cablevision_Altice_Workflow": {"host": "**************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100M", "target": "/DATG_VOD"}, "Alta_Fiber_Workflow": {"host": "**************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/storage/aspera/drop"}, "Armstrong_Workflow": {"host": "************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "Astound_RCN_Workflow": {"host": "***********", "port": "22", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "ATT_Deluxe_Workflow": {"host": "aspera-live.deluxeone.com", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "300000", "target": "/ATT/STB"}, "Blue_Ridge_Workflow": {"host": "************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/DATG_VOD"}, "BlueStream_Fiber_Workflow": {"host": "**************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "Breezeline_Workflow": {"host": "**************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/vod/abcipvod"}, "Buckeye_Workflow": {"host": "*************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "C-Spire_Workflow": {"host": "cspire-vod-prod.cspire.com", "port": "22", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "40000", "target": "/"}, "COX_Workflow": {"host": "*************", "port": "33001", "username": "", "pwd_or_key": "/opt/aspera/var/config/orchestrator/certs/cox_asp-disney", "authentication": "Public Key", "target_rate": "100000", "target": "/"}, "CSI_Digital_Workflow": {"host": "**************", "port": "33001", "username": "TBD", "pwd_or_key": "TBD", "authentication": "Password", "target_rate": "100000", "target": "/home/<USER>"}, "Frontier_Workflow": {"host": "*************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "GCI_Workflow": {"host": "***********", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "50000", "target": "/ingested"}, "HBC_Workflow": {"host": "**************", "port": "22", "username": "", "pwd_or_key": "TBD", "authentication": "Password", "target_rate": "100000", "target": "/mnt/vod_backup/fx_ngc"}, "Logic_Workflow": {"host": "*************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "50000", "target": "/"}, "Mediacom_Workflow": {"host": "**************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "50000", "target": "/"}, "MCTV_Workflow": {"host": "************", "port": "22", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/fxngc"}, "OzarksGo_Workflow": {"host": "*************", "port": "33001", "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": "100000", "target": "/"}, "Vermont_Telephone_Workflow": {"host": "**************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/DATG_VOD"}, "Verizon_Lab_Workflow": {"host": "*************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/DATG_VOD"}, "Verizon_STB_Workflow": {"host": "**************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/VOD"}, "Vubiquity_WOW_Workflow": {"host": "aspera.vubiquity.com", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/DATG_VOD"}, "MobiTV_Workflow": {"host": "**************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/"}, "SECTV_WG": {"host": "*************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/DATG_VOD"}, "Tangerine_Allo_Workflow": {"host": "************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Password", "target_rate": 100000, "target": "/"}, "TDS_Broadband_Workflow": {"host": "*************", "port": 22, "username": "", "pwd_or_key": "", "authentication": "Public Key", "target_rate": 100000, "target": "/"}, "TDS_Telecom_Workflow": {"host": "************", "port": 22, "username": "", "pwd_or_key": "", "authentication": "Public Key", "target_rate": 100000, "target": "/"}, "WideOpenWest_Detroit_Workflow": {"host": "**************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Public Key", "target_rate": 120000, "target": "/"}, "WideOpenWest_WestPoint_Workflow": {"host": "**************", "port": 33001, "username": "", "pwd_or_key": "", "authentication": "Public Key", "target_rate": 120000, "target": "/"}}