{"id": "1", "configuration_rules": {"config": {"destination_bucket": "some_destination_bucket", "landing_bucket": "some_landing_bucket", "output_filename": "{old_name}_{current_date:%Y-%m-%d}", "secrets_manager": "some_secrets", "status_table_name": "some_status_table", "timezone": "US/Pacific"}, "rules": {"Affiliate_Name": {"delivery_name": "Affiliate_Example", "faspex": false, "has_HD": true, "has_SD": true, "HD_movies_category": "hd_movies_key", "HD_series_category": "hd_season_key", "SD_movies_category": "sd_movies_key", "SD_series_category": "sd_season_key", "remove_attributes": {"filter_key": "@Name", "keys": ["Ad_Content_ID", "Season_Number", "Series_Name", "TMS_Episode_ID", "TMS_Series_ID"], "path": "ADI.Asset.Metadata.App_Data"}, "replace_attributes_by": [{"filter_by": "Value", "key_value_filter_by": "affiliateHDPCT2", "value_filter_by": "Provider_Content_Tier"}], "update_attributes": [{"key_value_filter": "@Value", "value_filter": "Original_Air_Date", "value_update": "T00:00:00"}], "skip_content": {"validation": true, "values_skip": ["FXX", "FXM"]}, "tar": false, "limit_length": {"key_filter": "@Value", "length": 32, "validation": true, "value_filter": "Title"}}}}}