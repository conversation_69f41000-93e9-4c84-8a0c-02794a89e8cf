import json
import os
from collections import OrderedDict
from typing import Any, List

import xmltodict
from glom import glom

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__)))


def affiliates_config() -> dict:
    base_dir_on_permises = os.path.join(PROJECT_ROOT, "affiliates", "config_files")
    file_path = os.path.join(base_dir_on_permises, "affiliates_config.json")

    with open(file_path, "r") as f:
        return json.load(f)


def data_xml(read_local_file_path: str) -> dict:
    with open(read_local_file_path, "r") as f:
        return f.read()


aff_confg = affiliates_config()

xml_obj = data_xml(
    "/Users/<USER>/Library/CloudStorage/OneDrive-Globant/disnay/projects/tacdev-projects/express-lane/tests/affiliates/files/input/CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml"
)

rules = aff_confg.get("configuration_rules").get("rules")

xml_to_dict = xmltodict.parse(xml_obj)

from glom import Iter, T, glom


def extract_app_data_value(data, path, name):
    spec = (path, Iter().filter(lambda x: x.get("@Name") == name).map("@Value").first())
    return glom(data, spec, default=None)


# Extract from App_Data
value1 = extract_app_data_value(
    xml_to_dict, "master.ADI.Asset.Metadata.App_Data", "HDContent"
)
print("HDContent:", value1)  # Output: yes


from glom import Iter, glom

data = {
    "master": {
        "ADI": {
            "Metadata": {
                "AMS": {
                    "@Asset_Class": "package",
                    "@Asset_ID": "NGTV1098663800001201",
                    "@Asset_Name": "NGTV1098663800001201_package",
                    "@Creation_Date": "2025-03-17",
                    "@Description": "NGCNCCC102_HD_package asset",
                    "@Product": "MOD",
                    "@Provider": "NATGEO_HD",
                    "@Provider_ID": "natgeochannel.com",
                    "@Version_Major": "1",
                    "@Version_Minor": "0",
                    "@Verb": "",
                },
                "App_Data": [
                    {
                        "@App": "MOD",
                        "@Name": "Provider_Content_Tier",
                        "@Value": "NATIONALGEOGRAPHIC_HD_5",
                    },
                    {
                        "@App": "MOD",
                        "@Name": "Metadata_Spec_Version",
                        "@Value": "CableLabsVOD1.1",
                    },
                ],
            },
            "Asset": {
                "Metadata": {
                    "AMS": {
                        "@Asset_Class": "title",
                        "@Asset_ID": "NGTV1098663800001202",
                        "@Asset_Name": "NGTV1098663800001202_title",
                        "@Creation_Date": "2025-03-17",
                        "@Description": "NGCNCCC102_HD_title",
                        "@Product": "MOD",
                        "@Provider": "NATGEO_HD",
                        "@Provider_ID": "natgeochannel.com",
                        "@Version_Major": "1",
                        "@Version_Minor": "0",
                        "@Verb": "",
                    },
                    "App_Data": [
                        {"@App": "MOD", "@Name": "Type", "@Value": "title"},
                        {
                            "@App": "MOD",
                            "@Name": "Title",
                            "@Value": "Crash Course Cuisine with Hudson Yang",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Title_Sort_Name",
                            "@Value": "CrashCour 102 HD",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Title_Brief",
                            "@Value": "CrashCour 102 HD",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Summary_Short",
                            "@Value": "Hudson travels to Houston, Texas, where he learns slow cooking and how to use a wok.",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Summary_Long",
                            "@Value": "Hudson travels to the land of fire and smoke - Houston, Texas - where he learns skills like slow cooking and how to use a wok.",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Series_Name",
                            "@Value": "Crash Course Cuisine with Hudson Yang",
                        },
                        {"@App": "MOD", "@Name": "Season_Number", "@Value": "1"},
                        {
                            "@App": "MOD",
                            "@Name": "Provider_QA_Contact",
                            "@Value": "<EMAIL>",
                        },
                        {"@App": "MOD", "@Name": "Suggested_Price", "@Value": "0.00"},
                        {
                            "@App": "MOD",
                            "@Name": "Maximum_Viewing_Length",
                            "@Value": "01:00:00",
                        },
                        {"@App": "MOD", "@Name": "Episode_Name", "@Value": "Fired Up"},
                        {"@App": "MOD", "@Name": "Episode_ID", "@Value": "2"},
                        {"@App": "MOD", "@Name": "Billing_ID", "@Value": "0000"},
                        {"@App": "MOD", "@Name": "Display_Run_Time", "@Value": "00:44"},
                        {"@App": "MOD", "@Name": "Run_Time", "@Value": "00:44:31"},
                        {
                            "@App": "MOD",
                            "@Name": "Original_Air_Date",
                            "@Value": "2025-03-16",
                        },
                        {"@App": "MOD", "@Name": "Year", "@Value": "2025"},
                        {
                            "@App": "MOD",
                            "@Name": "Licensing_Window_Start",
                            "@Value": "2025-03-17T00:00:00",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Licensing_Window_End",
                            "@Value": "2025-04-20T23:59:59",
                        },
                        {"@App": "MOD", "@Name": "Preview_Period", "@Value": "0"},
                        {"@App": "MOD", "@Name": "Display_As_New", "@Value": "4"},
                        {
                            "@App": "MOD",
                            "@Name": "Display_As_Last_Chance",
                            "@Value": "3",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Studio",
                            "@Value": "National Geographic",
                        },
                        {"@App": "MOD", "@Name": "Closed_Captioning", "@Value": "Y"},
                        {
                            "@App": "MOD",
                            "@Name": "Category",
                            "@Value": "TV Shows/By Network/National Geographic/Crash Course HD",
                        },
                        {"@App": "MOD", "@Name": "Genre", "@Value": "Food"},
                        {"@App": "MOD", "@Name": "Rating", "@Value": "TV-PG"},
                        {
                            "@App": "MOD",
                            "@Name": "TMS_Episode_ID",
                            "@Value": "EP055607180002",
                        },
                        {
                            "@App": "MOD",
                            "@Name": "Ad_Content_ID",
                            "@Value": "10986638-U",
                        },
                    ],
                },
                "Asset": [
                    {
                        "Metadata": {
                            "AMS": {
                                "@Asset_Class": "movie",
                                "@Asset_ID": "NGTV1098663800001203",
                                "@Asset_Name": "NGTV1098663800001203_movie",
                                "@Creation_Date": "2025-03-17",
                                "@Description": "NGCNCCC102_HD_movie",
                                "@Product": "MOD",
                                "@Provider": "NATGEO_HD",
                                "@Provider_ID": "natgeochannel.com",
                                "@Version_Major": "1",
                                "@Version_Minor": "0",
                                "@Verb": "",
                            },
                            "App_Data": [
                                {"@App": "MOD", "@Name": "Type", "@Value": "movie"},
                                {
                                    "@App": "MOD",
                                    "@Name": "Audio_Type",
                                    "@Value": "Dolby 5.1",
                                },
                                {"@App": "MOD", "@Name": "Languages", "@Value": "en"},
                                {"@App": "MOD", "@Name": "Bit_Rate", "@Value": "15001"},
                                {"@App": "MOD", "@Name": "HDContent", "@Value": "Y"},
                                {
                                    "@App": "MOD",
                                    "@Name": "trickModesRestricted",
                                    "@Value": "FF",
                                },
                                {
                                    "@App": "MOD",
                                    "@Name": "Content_CheckSum",
                                    "@Value": "8a953d257407885ab030dd56125c3505",
                                },
                                {
                                    "@App": "MOD",
                                    "@Name": "Content_FileSize",
                                    "@Value": "5010524112",
                                },
                            ],
                        },
                        "Content": {
                            "@Value": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.mpg"
                        },
                    },
                    {
                        "Metadata": {
                            "AMS": {
                                "@Asset_Class": "poster",
                                "@Asset_ID": "NGTV1098663800001204",
                                "@Asset_Name": "NGTV1098663800001204_poster",
                                "@Creation_Date": "2025-03-17",
                                "@Description": "NGTV1098663800001204 still asset",
                                "@Product": "MOD",
                                "@Provider": "NATGEO_HD",
                                "@Provider_ID": "natgeochannel.com",
                                "@Version_Major": "1",
                                "@Version_Minor": "0",
                                "@Verb": "",
                            },
                            "App_Data": [
                                {"@App": "MOD", "@Name": "Type", "@Value": "poster"},
                                {
                                    "@App": "MOD",
                                    "@Name": "Content_CheckSum",
                                    "@Value": "528c98a20b2b9de0a01116d65027ac1c",
                                },
                                {
                                    "@App": "MOD",
                                    "@Name": "Content_FileSize",
                                    "@Value": "22724",
                                },
                            ],
                        },
                        "Content": {
                            "@Value": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.bmp"
                        },
                    },
                ],
            },
        },
        "affiliate_list": {
            "affiliate": [
                "Alta Fiber",
                "Antietam",
                "Armstrong",
                "Astound (RCN)",
                "Blue Ridge",
                "BlueStream Fiber",
                "Breezeline",
                "Buckeye",
                "C-Spire",
                "CSI Digital",
                "Cablevision (Altice)",
                "Cinergy Metronet",
                "Cox",
                "EPB",
                "Frontier",
                "GCI",
                "HBC",
                "Hotwire",
                "Logic",
                "MCTV",
                "Mediacom",
                "MobiTV",
                "SECTV",
                "SECTV LehighValley",
                "SECTV Sparta NJ",
                "TDS Broadband",
                "TDS Telecom",
                "Tangerine (Allo)",
                "Verizon Lab",
                "Verizon STB",
                "Vermont Telephone",
                "Vubiquity WOW",
            ]
        },
        "package_info": {
            "content_name": "Crash Course Cuisine with Hudson Yang",
            "network_name": "National Geographic",
        },
        "metadata_season": {
            "metadata": [
                {
                    "@name": "affiliateSeriesSeasonHDMappingStrings",
                    "value": "TV Shows/By Network/National Geographic/Crash Course HD",
                },
                {
                    "@name": "affiliateSeriesSeasonSDMappingStrings",
                    "value": "TV Shows/By Network/National Geographic/Crash Course",
                },
                {
                    "@name": "altaFiberSeriesCategoryMapping",
                    "value": "Free On Demand/By Channel/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "armstrongSeriesSeasonCategoryMapping",
                    "value": "TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Series/Series A - F/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "astoundSeriesSeasonCategoryMapping",
                    "value": "By Network/NatGeo/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "bluestreamSeriesCategoryMapping",
                    "value": "TV/Cable Networks J-N/National Geographic/Crash Course Cuisine with Hudson Yang|By Network/J-N/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "breezelineSeriesCategoryMapping",
                    "value": "Free TV Shows/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "buckeyeSeriesCategoryMapping",
                    "value": "TV Entertainment/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "cablevisionAlticeSeriesHDCategoryMapping",
                    "value": "NatGeo/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "coxSeriesSeasonCategorySDMapping",
                    "value": "TV/TV Networks/Networks N-S/Nat Geo/Crash Course Cuisine with Hudson Yang|TV/TV Shows/A-C/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "coxSeriesSeasonCategoryHDMapping",
                    "value": "TV/TV Networks/Networks N-S/Nat Geo/Crash Course Cuisine with Hudson Yang HD|TV/TV Shows/A-C/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "cSpireSeriesSeasonCategoryMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "epbSeriesSeasonCategoryHDMapping",
                    "value": "TV/Networks/National Geographic/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "epbSeriesSeasonCategorySDMapping",
                    "value": "TV/Networks/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "frontierSeriesSeasonCategoryHDMapping",
                    "value": "Free & Premium/TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "frontierSeriesSeasonCategorySDMapping",
                    "value": "Free & Premium/TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "gciSeriesSeasonCategoryMapping",
                    "value": "TV/Networks/National Geographic/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "hbcSeriesSeasonCategoryHDMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course HD",
                },
                {
                    "@name": "hbcSeriesSeasonCategorySDMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course",
                },
                {
                    "@name": "hotwireSeriesSeasonCategoryHDMapping",
                    "value": "Networks on Demand/National Geographic/Crash Course Cuisine with Hudson Yang HD|Networks on Demand HD/National Geographic/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "hotwireSeriesSeasonCategorySDMapping",
                    "value": "Networks on Demand/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "mctvSeriesSeasonCategoryMapping",
                    "value": "TV Networks/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "mediacomSeriesSeasonCategoryMapping",
                    "value": "TV SHOWS A-Z/A-C/CRASH COURSE CUISINE WITH HUDSON YANG|TV/TV NETWORKS/NATIONAL GEOGRAPHIC/CRASH COURSE CUISINE WITH HUDSON YANG",
                },
                {
                    "@name": "mobiSeriesCategoryMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course HD",
                },
                {
                    "@name": "sectvSeriesSeasonCategoryHDMapping",
                    "value": "SECV On Demand/Free On Demand A-Z/National Geographic HD/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "sectvSeriesSeasonCategorySDMapping",
                    "value": "SECV On Demand/Free On Demand A-Z/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "sectvLehighSeriesSeasonCategoryHDMapping",
                    "value": "Service Electric VOD/Free OnDemand A-Z/National Geographic HD/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "sectvLehighSeriesSeasonCategorySDMapping",
                    "value": "Service Electric VOD/Free OnDemand A-Z/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "sectvSpartaSeriesSeasonCategoryHDMapping",
                    "value": "VODlink Root/Service Electric/FREE/Cable TV Favorites/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "sectvSpartaSeriesSeasonCategorySDMapping",
                    "value": "VODlink Root/Service Electric/FREE/Cable TV Favorites/National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "tangerineAlloSeriesCategoryMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course HD",
                },
                {
                    "@name": "tdsBroadbandSeriesSeasonCategoryMapping",
                    "value": "TV/TV Shows/A-C HD/Crash Course Cuisine with Hudson Yang|TV/TV Networks/National Geo HD/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "tdsTelecomSeriesSeasonCategoryHDMapping",
                    "value": "TV Shows/C/Crash Course Cuisine with Hudson Yang HD|TV Shows/TV Network/Nat Geo/Crash Course Cuisine with Hudson Yang HD",
                },
                {
                    "@name": "tdsTelecomSeriesSeasonCategorySDMapping",
                    "value": "TV Shows/C/Crash Course Cuisine with Hudson Yang|TV Shows/TV Network/Nat Geo/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "verizonSeriesSeasonCategoryHDMapping",
                    "value": "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Network/N - R/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Show/A - C/Crash Course Cuisine with Hudson Yang|TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Network/N - R/Nat Geo/Nat Geo HD/Crash Course Cuisine with Hudson Yang|TV Shows/By Show/A - C/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "verizonSeriesSeasonCategorySDMapping",
                    "value": "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Network/N - R/Nat Geo/Nat Geo/Crash Course Cuisine with Hudson Yang|Free & Premium/TV Shows/By Show/A - C/Crash Course Cuisine with Hudson Yang|TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Shows/By Network/N - R/Nat Geo/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Shows/By Show/A - C/Crash Course Cuisine with Hudson Yang",
                },
                {
                    "@name": "vermontSeriesSeasonCategoryHDMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course HD",
                },
                {
                    "@name": "vermontSeriesSeasonCategorySDMapping",
                    "value": "TV Shows/By Network/National Geographic/Crash Course",
                },
                {
                    "@name": "vubiquityWOWSeriesCategoryHDMapping",
                    "value": "HD On Demand/NG HD/Crash Course Cuisine with Hudson Yang HD",
                },
            ]
        },
        "metadata_network": {
            "metadata": [
                {"@name": "networkCValue", "value": "C3"},
                {"@name": "networkDValue", "value": "D4"},
                {"@name": "affiliateProductCode", "value": "MOD"},
                {"@name": "affiliateProviderID", "value": "natgeochannel.com"},
                {"@name": "affiliateHDPCT", "value": "NATIONALGEOGRAPHIC_HD_5"},
                {"@name": "affiliateHDPCT2", "value": "NATIONALGEOGRAPHIC_EXPANDED_HD"},
                {"@name": "affiliateSDPCT", "value": "NATIONALGEOGRAPHIC_10"},
                {"@name": "affiliateSDPCT2", "value": "NATIONALGEOGRAPHIC_EXPANDED"},
                {"@name": "affiliateProviderSD", "value": "NATGEO"},
                {"@name": "affiliateProviderSDC3", "value": "NATIONALGEOGRAPHIC_C3"},
                {"@name": "affiliateProviderHD", "value": "NATGEO_HD"},
                {"@name": "affiliateProviderHDC3", "value": "NATGEO_C3_HD"},
                {"@name": "cMSLongFormFolderPath", "value": "video_fep_ngv"},
                {"@name": "coxProvider", "value": "NAT_GEOHD"},
                {"@name": "coxProductCode", "value": "FZHD"},
                {"@name": "mediacomProvider", "value": "NAT_GEO_HD"},
                {"@name": "cMSShortFormFolderPath", "value": "video_ngv"},
                {"@name": "cMSLongFormFolderPathAkamai", "value": "video_fep_ngv"},
                {"@name": "cMSShortFormFolderPathAkamai", "value": "video_ngv"},
                {"@name": "cablelabProvider", "value": "NATIONAL GEOGRAPHIC"},
                {"@name": "cablelabProviderID", "value": "natgeochannel.com"},
                {"@name": "cablelabAssetID4LetterPrefix", "value": "NGTV"},
                {
                    "@name": "providerQAContact",
                    "value": "<EMAIL>",
                },
                {"@name": "huluNielsenWatermarkingDSRCID", "value": "48527"},
                {
                    "@name": "huluNielsenWatermarkingDSRCName",
                    "value": "VOD NatGeo Hulu",
                },
                {"@name": "huluNielsenWatermarkingSID0", "value": "7903"},
                {"@name": "mVPDNielsenWatermarkingDSRCID", "value": "16117"},
                {"@name": "mVPDNielsenWatermarkingDSRCName", "value": "VOD Nat Geo"},
                {"@name": "mVPDNielsenWatermarkingSID", "value": "7899"},
                {"@name": "nielsenSVODDistributor", "value": "NATGEO SVOD"},
                {
                    "@name": "upLynkExternalID",
                    "value": "e6dbd32077b143a4b9cb024b791f2930",
                },
            ]
        },
        "metadata_series": {
            "metadata": [
                {"@name": "affiliateSeriesTitleBrief", "value": "CrashCour"},
                {"@name": "affiliateSeriesGenreCodes", "value": "Food"},
                {"@name": "comscoreC6", "value": "NCCC"},
                {
                    "@name": "dATGGoPubFolder",
                    "value": "CrashCourseCuisinewithHudsonYang",
                },
            ]
        },
        "metadata_package": {
            "metadata": [
                {"@name": "episodeTitle", "value": "Fired Up"},
                {"@name": "productionNumber", "value": "102"},
                {"@name": "tmsId", "value": "EP055607180002"},
                {"@name": "keywords", "value": "Food, Documentary"},
                {"@name": "tVRating", "value": "TV-PG"},
                {"@name": "tVRatingDescriptors", "value": "Language"},
                {"@name": "trafficCode", "value": "NCCC94736X1"},
                {"@name": "episodeNumber", "value": "2"},
                {"@name": "affiliateSTBSunrise", "value": "2025-03-17T00:00:00"},
                {"@name": "affiliateSTBSunset", "value": "2025-04-20T23:59:59"},
                {"@name": "affiliateAdContentID", "value": "10986638-U"},
                {
                    "@name": "affiliateManifestID",
                    "value": "natgeochannel.com:NGTV1098663800001101",
                },
                {"@name": "affiliateHDAssetID", "value": "NGTV1098663800001201"},
                {"@name": "affiliateHDTitleID", "value": "NGTV1098663800001202"},
                {"@name": "affiliateHDMovieID", "value": "NGTV1098663800001203"},
                {"@name": "affiliateHDPosterID", "value": "NGTV1098663800001204"},
                {"@name": "affiliateHDCCID", "value": "NGTV1098663800001205"},
                {
                    "@name": "affiliateBaseFilename",
                    "value": "CrashCourseCuisinewi_S01E02_D4_10986638",
                },
                {"@name": "affiliateBaseFoldername", "value": "10986638_00001"},
                {"@name": "affiliateBaseFoldernameSD", "value": "10986638_SD_00001"},
                {
                    "@name": "affiliateDeliveryList",
                    "value": "Alta Fiber Antietam Armstrong Astound (RCN) Blue Ridge BlueStream Fiber Breezeline Buckeye C-Spire CSI Digital Cablevision (Altice) Cinergy Metronet Cox EPB Frontier GCI HBC Hotwire Logic MCTV Mediacom MobiTV SECTV SECTV LehighValley SECTV Sparta NJ TDS Broadband TDS Telecom Tangerine (Allo) Verizon Lab Verizon STB Vermont Telephone Vubiquity WOW",
                },
                {"@name": "affiliateSDAssetID", "value": "NGTV1098663800001101"},
                {"@name": "affiliateSDTitleID", "value": "NGTV1098663800001102"},
                {"@name": "affiliateSDMovieID", "value": "NGTV1098663800001103"},
                {
                    "@name": "cablevisionAlticeDeliveryFolder",
                    "value": "National Geographic/Crash Course Cuisine with Hudson Yang",
                },
                {"@name": "affiliateSDPosterID", "value": "NGTV1098663800001104"},
                {"@name": "affiliateSDCCID", "value": "NGTV1098663800001105"},
                {
                    "@name": "mCTVDeliveryFolder",
                    "value": "/TV Networks/National Geographic",
                },
            ]
        },
    }
}


# ---------
product_values = glom(xml_to_dict, "**.@Product")
print(product_values)

# ---------
value_finder = glom(xml_to_dict, "master.package_info.network_name", default=None)
print(value_finder)


# ---------
def extract_metadata_value(data, path, name):
    spec = (path, Iter().filter(lambda x: x.get("@name") == name).map("value").first())
    return glom(data, spec, default=None)


value2 = extract_metadata_value(
    xml_to_dict, "master.metadata_network.metadata", "coxProductCode"
)
print("coxProductCode:", value2)  # Output: Reach

# ---------


def extract_value(data, name):
    """
    Recursively finds any App_Data dict with @Name == name and returns its @Value.
    Works for arbitrarily nested data.
    """
    spec = (
        "**.App_Data",  # search recursively for any App_Data block
        Iter()
        .flatten()
        .filter(lambda x: isinstance(x, dict) and x.get("@Name") == name)
        .map("@Value")
        .first(),
    )
    return glom(data, spec, default=None)


value = extract_value(xml_to_dict, "HDContent")
print("HDContent:", value)  # Y


from collections import deque


class FlattenObj:
    def __init__(self, data):
        self.data = data

    def flatten_structure_obj(self, bfs=False):
        result = []
        stack = deque([([], self.data)])  # deque instead of list

        while stack:
            if bfs:
                path, current = stack.popleft()  # BFS
            else:
                path, current = stack.pop()  # DFS (original behavior)

            if isinstance(current, dict):
                for k in reversed(list(current.keys())):
                    stack.append((path + [k], current[k]))
            elif isinstance(current, list):
                for idx in reversed(range(len(current))):
                    elem = current[idx]
                    stack.append((path + [idx], elem))
            else:
                result.append((path, current))

        result.sort(key=lambda x: x[0])
        return result


flatten_obj = FlattenObj(xml_to_dict)

print(flatten_obj.flatten_structure_obj())
