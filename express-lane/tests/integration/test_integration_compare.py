import logging
from datetime import datetime
from itertools import zip_longest
from unittest.mock import patch

import pytest
from lxml.etree import parse
from mvpd_affiliate_prep_5_1_1 import main

from src.express_lane_flow import express_lane_flow

# Reduce Prefect log level to avoid rich console crash during pytest teardown
logging.getLogger("prefect").setLevel(logging.WARNING)


class IntegrationCompare:

    def __init__(self):
        pass

    def run_on_permise_file(
        self,
        local_dict_bucket: list,
        local_dict_bucket_IncredibleNorthernVe: list,
        output_dir: str,
    ):
        resolutions = "HD"
        date_format = datetime.now().strftime("%y_%m_%d")
        base_name = "CrashCourseCuisinewi_S01E02_D4_10986638_HD_delivery"
        output_name = f"{output_dir}/on_permises/{base_name}_{date_format}.xml"
        output = output_name

        main(local_dict_bucket[0], output, resolutions)

        resolutions = "HD"
        date_format = datetime.now().strftime("%y_%m_%d")
        base_name = "IncredibleNorthernVe_S01E02_D4_10976545_HD_delivery"
        output_name = f"{output_dir}/on_permises/{base_name}_{date_format}.xml"
        output = output_name

        main(local_dict_bucket_IncredibleNorthernVe[0], output, resolutions)

    def run_express_lane(
        self,
        local_dict_bucket: list,
        local_dict_bucket_IncredibleNorthernVe: list,
        output_dir: str,
    ) -> None:
        """
        Initializes all the services and configurations and runs the flow
        according to the configuration obtained.
        """

        main_path = local_dict_bucket[0]
        base_name = "CrashCourseCuisinewi_S01E02_D4_10986638_HD_delivery"
        date_format = datetime.now().strftime("%y_%m_%d")

        output_name = f"{output_dir}/express_lane/{base_name}_{date_format}.xml"

        express_lane_flow(
            event={
                "Records": [
                    {
                        "s3": {
                            "object": {
                                "key": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
                            },
                            "bucket": {"name": "express-lane-test"},
                        },
                        "prefect_args": {
                            "flow_id": "0817abd8-e6fa-4408-83ae-c945b13c0238",
                            "deployment_id": "7022d887-37a0-40fc-890b-0fe506c1284e",
                        },
                    }
                ],
                "test": {
                    "read_local_file_path": main_path,
                    "load_local_file_path": output_name,
                },
            },
            local=True,
        )

        main_path = local_dict_bucket_IncredibleNorthernVe[0]
        base_name = "IncredibleNorthernVe_S01E02_D4_10976545_HD_delivery"
        date_format = datetime.now().strftime("%y_%m_%d")

        output_name = f"{output_dir}/express_lane/{base_name}_{date_format}.xml"

        express_lane_flow(
            event={
                "Records": [
                    {
                        "s3": {
                            "object": {
                                "key": "IncredibleNorthernVe_S01E02_D4_10976545_HD.xml",
                            },
                            "bucket": {"name": "express-lane-test"},
                        },
                        "prefect_args": {
                            "flow_id": "0817abd8-e6fa-4408-83ae-c945b13c0238",
                            "deployment_id": "7022d887-37a0-40fc-890b-0fe506c1284e",
                        },
                    }
                ],
                "test": {
                    "read_local_file_path": main_path,
                    "load_local_file_path": output_name,
                },
            },
            local=True,
        )


class XMLComparator:
    def __init__(self, file1, file2):
        self.tree1 = parse(file1)
        self.tree2 = parse(file2)
        self.root1 = self.tree1.getroot()
        self.root2 = self.tree2.getroot()
        self.differences = []

    def normalize(self, elem):
        elem.tag = elem.tag.strip()
        elem.text = (elem.text or "").strip()
        elem.tail = (elem.tail or "").strip()

        sorted_attribs = sorted(elem.attrib.items())
        elem.attrib.clear()
        for k, v in sorted_attribs:
            elem.set(k, v)

        for child in elem:
            self.normalize(child)

        elem[:] = sorted(elem, key=lambda e: (e.tag, sorted(e.attrib.items())))

    def compare_attributes(self, e1, e2, current_path):
        if e1.attrib != e2.attrib:
            all_keys = set(e1.attrib) | set(e2.attrib)
            for key in all_keys:
                v1 = e1.attrib.get(key, "<MISSING>")
                v2 = e2.attrib.get(key, "<MISSING>")
                if v1 != v2:
                    self.differences.append(
                        f"[{current_path}] Attribute '{key}' mismatch: File1='{v1}' vs File2='{v2}'\n"
                        f"  Line File1: {e1.sourceline}, Line File2: {e2.sourceline}"
                    )

    def elements_equal(self, e1, e2, path="/"):
        current_path = f"{path}/{e1.tag}"

        if e1.tag != e2.tag:
            self.differences.append(
                f"[{current_path}] Tag mismatch: {e1.tag} != {e2.tag}"
            )

        if (e1.text or "").strip() != (e2.text or "").strip():
            self.differences.append(
                f"[{current_path}] Text mismatch: '{(e1.text or '').strip()}' != '{(e2.text or '').strip()}'\n"
                f"  Line File1: {e1.sourceline}, Line File2: {e2.sourceline}"
            )

        self.compare_attributes(e1, e2, current_path)

        if len(e1) != len(e2):
            context = self.get_context_info(e1)
            e1_children = [f"{child.tag} {dict(child.attrib)}" for child in e1]
            e2_children = [f"{child.tag} {dict(child.attrib)}" for child in e2]
            self.differences.append(
                f"[{current_path}] Children count mismatch: {len(e1)} != {len(e2)}\n"
                f"  Context: {context}\n"
                f"  Line File1: {e1.sourceline}, Line File2: {e2.sourceline}\n"
                f"  File1 children: {e1_children}\n"
                f"  File2 children: {e2_children}"
            )

        for c1, c2 in zip_longest(e1, e2):
            if c1 is None:
                self.differences.append(
                    f"[{current_path}] Extra element in File2: {c2.tag} {dict(c2.attrib)}"
                )
                continue
            if c2 is None:
                self.differences.append(
                    f"[{current_path}] Extra element in File1: {c1.tag} {dict(c1.attrib)}"
                )
                continue
            self.elements_equal(c1, c2, current_path)

    def compare(self):
        self.normalize(self.root1)
        self.normalize(self.root2)
        self.elements_equal(self.root1, self.root2)
        return not self.differences, self.differences

    def get_context_info(self, elem):
        while elem is not None:
            for key in ["ascp_client", "workgroup_name"]:
                if key in elem.attrib:
                    return f"{key}={elem.attrib[key]}"
            elem = elem.getparent()
        return "No affiliate context found"


@pytest.mark.order(1)
@patch("utils.DynamoUtils.DynamoDBClient.create_item")
@patch("utils.DynamoUtils.DynamoDBClient.update_item")
@patch("utils.DynamoUtils.DynamoDBClient.scan_items")
@patch("utils.SecretsManagerUtils.SecretsManagerUtils.get_secret_value")
def test_processes(
    mock_get_secret_value,
    mock_scan_items,
    mock_update_item,
    mock_create_item,
    affiliates_config,
    secrets_config,
    local_dict_bucket,
    local_dict_bucket_IncredibleNorthernVe,
    output_dir,
) -> None:

    mock_scan_items.return_value = [affiliates_config]

    mock_get_secret_value.return_value = secrets_config

    mock_create_item.return_value = None

    mock_update_item.return_value = None

    integreation_services = IntegrationCompare()
    integreation_services.run_on_permise_file(
        local_dict_bucket,
        local_dict_bucket_IncredibleNorthernVe,
        output_dir,
    )
    integreation_services.run_express_lane(
        local_dict_bucket,
        local_dict_bucket_IncredibleNorthernVe,
        output_dir,
    )


@pytest.mark.order(2)
def test_compare_xml(
    local_output_bucket_CrashCourseCuisinewi,
) -> None:

    local_output = local_output_bucket_CrashCourseCuisinewi

    print("--------- local_output ---->", local_output)

    file1 = local_output.get("express_lane")
    file2 = local_output.get("on_permises")

    comparator = XMLComparator(file1, file2)
    result, message = comparator.compare()
    if result:
        assert result, "✅ XML files are equivalent"
        print("✅ XML files are equivalent")
    else:
        assert result, f"❌ XML files are not equivalent: {message}"
        print(f"❌ Difference found: {message}")


@pytest.mark.order(3)
def test_compare_xml_IncredibleNorthernVe(
    local_output_bucket_IncredibleNorthernVe,
) -> None:

    local_output = local_output_bucket_IncredibleNorthernVe

    print("--------- local_output ---->", local_output)

    file1 = local_output.get("express_lane")
    file2 = local_output.get("on_permises")

    comparator = XMLComparator(file1, file2)
    result, message = comparator.compare()
    if result:
        assert result, "✅ XML files are equivalent"
        print("✅ XML files are equivalent")
    else:
        assert result, f"❌ XML files are not equivalent: {message}"
        print(f"❌ Difference found: {message}")
