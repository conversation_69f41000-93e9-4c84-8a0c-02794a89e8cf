import glob
import json
import os

import pytest


@pytest.fixture
def global_variables(secrets_managerUtils, rules) -> dict:
    return {
        "id": "CrashCourseCuisinewi_S01E02_D4_10986638_HD",
        "secrets_manager": secrets_managerUtils,
        "event_settings_rules": rules,
        "config_rules": rules.get("config_rules"),
    }


@pytest.fixture
def global_variables_ATT_DELUX(secrets_managerUtils, rules) -> dict:
    return {
        "id": "IncredibleNorthernVe_S01E02_D4_10976545_HD",
        "secrets_manager": secrets_managerUtils,
        "event_settings_rules": rules,
        "config_rules": rules.get("config_rules"),
    }


@pytest.fixture
def target_dir(root_path) -> list:
    project_base_path = root_path
    target_dir = os.path.join(
        project_base_path, "tests", "affiliates", "files", "input"
    )

    return target_dir


@pytest.fixture
def local_bucket(target_dir):
    def file_selection(file_select):
        files = {
            "CrashCourseCuisinewi_S01E02_D4_10986638_HD": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
            "IncredibleNorthernVe_S01E02_D4_10976545_HD": "IncredibleNorthernVe_S01E02_D4_10976545_HD.xml",
        }

        file_path = f"{target_dir}/{files.get(file_select)}"

        with open(file_path, "r", encoding="utf-8") as file:
            return file.read()

    return file_selection


@pytest.fixture
def root_path():
    return os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))


@pytest.fixture
def output_dir(root_path):
    project_base_path = root_path
    target_dir = os.path.join(
        project_base_path, "tests", "affiliates", "files", "output"
    )
    return target_dir


PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__)))


@pytest.fixture
def get_files_list_to_test() -> list:
    target_dir = os.path.join(PROJECT_ROOT, "affiliates", "files", "input")

    all_files = [
        os.path.splitext(f)[0]
        for f in os.listdir(target_dir)
        if os.path.isfile(os.path.join(target_dir, f))
    ]

    return [target_dir, all_files]


@pytest.fixture
def dir_path(_root_path):
    project_base_path = _root_path

    def _build_path(dir_name: str) -> str:
        return os.path.join(
            project_base_path, "affiliates", "files", "output", dir_name
        )

    return _build_path


@pytest.fixture
def get_output_files_to_test(dir_path) -> dict:
    express_lane_target_dir = dir_path("express_lane")
    on_permises_target_dir = dir_path("on_permises")

    all_files_express_lane = [
        os.path.join(express_lane_target_dir, f)
        for f in os.listdir(express_lane_target_dir)
        if os.path.isfile(os.path.join(express_lane_target_dir, f))
    ]

    all_files_on_permises = [
        os.path.join(on_permises_target_dir, f)
        for f in os.listdir(on_permises_target_dir)
        if os.path.isfile(os.path.join(on_permises_target_dir, f))
    ]

    return {
        "all_files_express_lane": all_files_express_lane,
        "on_permises_target_dir": all_files_on_permises,
    }


def _find_xml_file(base_dir: str, base_name: str) -> str:
    """
    Find the latest XML file that starts with `base_name` in the specified `base_dir`.
    """
    pattern = os.path.join(base_dir, f"{base_name}.xml")
    matching_files = glob.glob(pattern)

    if not matching_files:
        raise FileNotFoundError(f"No XML files found matching: {pattern}")

    return matching_files


def _get_files_to_test() -> str:
    return os.path.join(PROJECT_ROOT, "affiliates", "files", "input")


def _find_latest_xml_file(base_dir: str, base_name: str) -> str:
    """
    Find the latest XML file in `base_dir` that starts with `base_name` and ends with a date pattern and `.xml`.
    """
    pattern = os.path.join(base_dir, f"{base_name}_*.xml")
    matching_files = glob.glob(pattern)

    if not matching_files:
        raise FileNotFoundError(f"No XML files found matching: {pattern}")

    # Sort files by last modified time descending
    matching_files.sort(key=os.path.getmtime, reverse=True)
    return matching_files[0]


@pytest.fixture
def local_dict_bucket() -> dict:
    base_name = "CrashCourseCuisinewi_S01E02_D4_10986638_HD"
    base_dir = _get_files_to_test()

    file_path = _find_xml_file(base_dir, base_name)

    return file_path


@pytest.fixture
def local_dict_bucket_IncredibleNorthernVe() -> dict:
    base_name = "IncredibleNorthernVe_S01E02_D4_10976545_HD"
    base_dir = _get_files_to_test()

    file_path = _find_xml_file(base_dir, base_name)

    return file_path


@pytest.fixture
def local_output_bucket_CrashCourseCuisinewi() -> dict:
    base_name = "CrashCourseCuisinewi_S01E02_D4_10986638_HD_delivery"
    base_dir_express_lane = os.path.join(
        PROJECT_ROOT, "affiliates", "files", "output", "express_lane"
    )

    base_dir_on_permises = os.path.join(
        PROJECT_ROOT, "affiliates", "files", "output", "on_permises"
    )

    file_path_express_lane = _find_latest_xml_file(base_dir_express_lane, base_name)
    file_path_on_permises = _find_latest_xml_file(base_dir_on_permises, base_name)

    return {
        "express_lane": file_path_express_lane,
        "on_permises": file_path_on_permises,
    }


@pytest.fixture
def local_output_bucket_IncredibleNorthernVe() -> dict:
    base_name = "IncredibleNorthernVe_S01E02_D4_10976545_HD_delivery"
    base_dir_express_lane = os.path.join(
        PROJECT_ROOT, "affiliates", "files", "output", "express_lane"
    )

    base_dir_on_permises = os.path.join(
        PROJECT_ROOT, "affiliates", "files", "output", "on_permises"
    )

    file_path_express_lane = _find_latest_xml_file(base_dir_express_lane, base_name)
    file_path_on_permises = _find_latest_xml_file(base_dir_on_permises, base_name)

    return {
        "express_lane": file_path_express_lane,
        "on_permises": file_path_on_permises,
    }


@pytest.fixture
def affiliates_config() -> dict:
    base_dir_on_permises = os.path.join(PROJECT_ROOT, "affiliates", "config_files")
    file_path = os.path.join(base_dir_on_permises, "affiliates_config.json")

    with open(file_path, "r") as f:
        return json.load(f)


@pytest.fixture
def secrets_config() -> dict:
    base_dir_on_permises = os.path.join(PROJECT_ROOT, "affiliates", "config_files")
    file_path = os.path.join(base_dir_on_permises, "secrets_sbx.json")

    with open(file_path, "r") as f:
        return json.load(f)


@pytest.fixture
def output_dir(root_path):
    project_base_path = root_path
    target_dir = os.path.join(
        project_base_path, "tests", "affiliates", "files", "output"
    )
    return target_dir
