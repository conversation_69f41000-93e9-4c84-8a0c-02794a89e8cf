import pytest
import xmltodict


@pytest.mark.order(4)
class TestAffiliates:

    def test_alta_fiber(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Alta_Fiber_Workflow"
        network_name = "National Geographic"
        category_value = "Free On Demand/By Channel/National Geographic/Crash Course Cuisine with Hudson Yang"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert ascp_client == delivery_name
                assert network_name not in rules.get("Alta Fiber").get(
                    "skip_content"
                ).get("values_skip")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_antietam(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Antietam_Cable_WG"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@workgroup_name") == delivery_name:
                workgroup_name = matched_affiliate.get("@workgroup_name")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert workgroup_name == delivery_name
                assert {} == rules.get("Antietam").get("skip_content")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_armstrong(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Armstrong_Workflow"
        network_name = "National Geographic"
        # split from TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Series/Series A - F/Crash Course Cuisine with Hudson Yang
        category_value_from_source = [
            "TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang",
            "TV Series/Series A - F/Crash Course Cuisine with Hudson Yang",
        ]

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                ascp = matched_affiliate.get("ascp")
                tar = matched_affiliate.get("changes")
                category_values = [
                    d.get("@Value")
                    for d in matched_affiliate.get("ADI", {})
                    .get("Asset", {})
                    .get("Metadata", {})
                    .get("App_Data", [])
                    if d.get("@Name") == "Category"
                ]

                assert ascp_client == delivery_name
                assert network_name not in rules.get("Armstrong").get(
                    "skip_content"
                ).get("values_skip")
                assert category_values == category_value_from_source
                assert type(ascp) == dict
                assert list(ascp.keys()) == [
                    "host",
                    "port",
                    "username",
                    "pwd_or_key",
                    "authentication",
                    "target_rate",
                    "target",
                ]
                assert tar == None

    def test_astound_RCN(self):
        delivery_name = "Astound_RCN_Workflow"

    def test_blue_Ridge(self):
        delivery_name = "Blue_Ridge_Workflow"

    def test_blueStream_fiber(self):
        delivery_name = "BlueStream_Fiber_Workflow"

    def test_breezeline(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Breezeline_Workflow"
        network_name = "National Geographic"
        # split from TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Series/Series A - F/Crash Course Cuisine with Hudson Yang
        category_value_from_source = [
            "Free TV Shows/National Geographic/Crash Course Cuisine with Hudson Yang",
        ]

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                ascp = matched_affiliate.get("ascp")
                tar = matched_affiliate.get("changes")
                category_values = [
                    d.get("@Value")
                    for d in matched_affiliate.get("ADI", {})
                    .get("Asset", {})
                    .get("Metadata", {})
                    .get("App_Data", [])
                    if d.get("@Name") == "Category"
                ]

                assert ascp_client == delivery_name
                assert network_name not in rules.get("Breezeline").get("skip_content")
                assert category_values == category_value_from_source
                assert type(ascp) == dict
                assert list(ascp.keys()) == [
                    "host",
                    "port",
                    "username",
                    "pwd_or_key",
                    "authentication",
                    "target_rate",
                    "target",
                ]
                assert tar == None

    def test_buckeye(self):
        delivery_name = "Buckeye_Workflow"

    def test_C_spire(self, affiliates_config, local_output_bucket_CrashCourseCuisinewi):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "C-Spire_Workflow"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")

                assert ascp_client == delivery_name
                assert {} == rules.get("C-Spire").get("skip_content")
                assert tar == None

    def test_cablevision_altice(self):
        delivery_name = "Cablevision_Altice_Workflow"

    def test_frontier(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Frontier_Workflow"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")

                assert ascp_client == delivery_name
                assert {} == rules.get("Frontier").get("skip_content")
                assert tar == None

    def test_wow_detroit(self, local_output_bucket_CrashCourseCuisinewi):
        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "WideOpenWest_Detroit_Workflow"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                assert ascp_client == None

    def test_wow_westpoint(self, local_output_bucket_CrashCourseCuisinewi):
        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "WideOpenWest_WestPoint_Workflow"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                assert ascp_client == None

    def test_vermont(self, affiliates_config, local_output_bucket_CrashCourseCuisinewi):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Vermont_Telephone_Workflow"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")

                assert ascp_client == delivery_name
                assert {} == rules.get("Vermont Telephone").get("skip_content")
                assert tar == None

    def test_claro(self, affiliates_config, local_output_bucket_CrashCourseCuisinewi):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "ClaroTV_WG"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@workgroup_name") == delivery_name:
                workgroup_name = matched_affiliate.get("@workgroup_name")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert workgroup_name == delivery_name
                assert {} == rules.get("ClaroTV").get("skip_content")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def tes_att_deluxe(
        self, affiliates_config, local_output_bucket_IncredibleNorthernVe
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_IncredibleNorthernVe.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "ATT_Deluxe_Workflow"
        network_name = "National Geographic"
        # split from TV Networks/Nat Geo/Crash Course Cuisine with Hudson Yang|TV Series/Series A - F/Crash Course Cuisine with Hudson Yang
        category_value_from_source = [
            "TV Shows/By Network/Nat Geo Wild/Northern Vets HD",
        ]

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                ascp = matched_affiliate.get("ascp")
                tar = matched_affiliate.get("changes")
                category_values = [
                    d.get("@Value")
                    for d in matched_affiliate.get("ADI", {})
                    .get("Asset", {})
                    .get("Metadata", {})
                    .get("App_Data", [])
                    if d.get("@Name") == "Category"
                ]

                assert ascp_client == delivery_name
                assert network_name not in rules.get("ATT Deluxe").get("skip_content")
                assert category_values == category_value_from_source
                assert type(ascp) == dict
                assert list(ascp.keys()) == [
                    "host",
                    "port",
                    "username",
                    "pwd_or_key",
                    "authentication",
                    "target_rate",
                    "target",
                ]
                assert tar == None

    def test_tds_broadband(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "TDS_Broadband_Workflow"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert ascp_client == delivery_name
                assert {} == rules.get("TDS Broadband").get("skip_content")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_gci(self, affiliates_config, local_output_bucket_CrashCourseCuisinewi):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "GCI_Workflow"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert ascp_client == delivery_name
                assert {} == rules.get("GCI").get("skip_content")
                assert category_dict.get("@Value") == category_value
                assert tar == None

    def test_xperi_mobi(self):
        # (We don´t have this one. Malcom will need to give us this info.)
        pass

    def test_blue_ridge(
        self, affiliates_config, local_output_bucket_CrashCourseCuisinewi
    ):
        rules = affiliates_config.get("configuration_rules", {}).get("rules", {})

        affiliate_assambler = local_output_bucket_CrashCourseCuisinewi.get(
            "express_lane"
        )

        with open(affiliate_assambler, "r") as f:
            obj = f.read()

        obj_parseer = xmltodict.parse(obj)

        mvpd_list = obj_parseer.get("clients", {}).get("rules", {}).get("mvpd", [])

        delivery_name = "Blue_Ridge_Workflow"
        category_value = "TV Shows/By Network/National Geographic/Crash Course HD"

        for matched_affiliate in mvpd_list[:]:
            if matched_affiliate.get("@ascp_client") == delivery_name:
                ascp_client = matched_affiliate.get("@ascp_client")
                tar = matched_affiliate.get("changes")
                category = (
                    matched_affiliate.get("ADI")
                    .get("Asset")
                    .get("Metadata")
                    .get("App_Data")
                )
                category_dict = next(
                    (d for d in category if d.get("@Name") == "Category"), None
                )

                assert ascp_client == delivery_name
                assert {} == rules.get("Blue Ridge").get("skip_content")
                assert category_dict.get("@Value") == category_value
                assert tar == None
