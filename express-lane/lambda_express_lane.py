from utils.config_settings.express_lane_config_variables import config_variables
from utils.LoggerService import logger_service
from utils.RequestsHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>


def lambda_handler(event: dict, _: str) -> None:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """

    logger_service.info("************ IZMA / Running Lambda Express Lane ************")
    logger_service.info(" IZMA / Running Lambda Express Lane with args: %s", event)

    try:
        config = config_variables(event=event)

        prefect = config.get("prefect")
        base_url = prefect.base_url
        prefect_api_deployment = prefect.prefect_api_deployment
        prefect_api_flow_run = prefect.prefect_api_flow_run
        deployment_id = prefect.deployment_id

        request_handler = RequestHandler(base_url=base_url)

        deployment_url = f"{prefect_api_deployment}/{deployment_id}"
        deployment_flow_run_url = f"{deployment_url}/{prefect_api_flow_run}"

        deployment_flow_run = request_handler.post(
            endpoint=deployment_flow_run_url, json={"parameters": {"event": event}}
        )

        logger_service.info(deployment_flow_run.json())

    except BaseException as error:
        error_message = f"{error}"
        logger_service.error(
            "IZMA ERROR: %s -- event settings: %s",
            error_message,
        )

    logger_service.info("IZMA / Lambda Express Lane finished")


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            "key": "CrashCourseCuisinewi_S01E02_D4_10986638_HD.xml",
                        },
                        "bucket": {"name": "express-lane-test"},
                    }
                }
            ],
            "test": {
                "read_local_file_path": "path/to/local/file/.xml",
                "load_local_file_path": "path/to/local/store/file/.xml",
            },
        },
        "",
    )
