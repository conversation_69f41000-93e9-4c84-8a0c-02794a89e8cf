# Welcome to your prefect.yaml file! You can use this file for storing and managing
# configuration for deploying your flows. We recommend committing this file to source
# control along with your flow code.

# Generic metadata about this project
name: tacdev-projects
prefect-version: 3.4.4

# build section allows you to manage and build docker images
build: null

# push section allows you to manage if and how this project is uploaded to remote locations
push: null

# pull section allows you to provide instructions for cloning this project in remote locations
# pull:
# - prefect.deployments.steps.git_clone:
#     repository: ''
#     branch: feature/REACH-2390-Develop-POC-CTS-Expresslane-dashboard
#     access_token: '{{ prefect.blocks.secret.deployment-prefect-test-prefect-test-flow-repo-token
#       }}'

# the deployments section allows you to provide configuration for deploying flows
deployments:
- name: express-lane-flow
  version: null
  tags: []
  concurrency_limit: null
  description: null
  entrypoint: /app/src/express_lane_flow.py:express_lane_flow
  parameters: {}
  work_pool: 
    name: express-lane-flow
    work_queue_name: null
    job_variables: {}
  # schedules:
  # - interval: 3600.0
  #   anchor_date: '2025-06-03T22:23:21.421032+00:00'
  #   timezone: UTC
  #   active: true



