[project]
name = "tacdev-projects"
version = "0.1.0"
description = ""
readme = ""
requires-python = ">=3.12"
dependencies = [
    "boto3==1.35.35",
    "certifi==2024.6.2",
    "colorama==0.4.6",
    "fuzzywuzzy>=0.18.0",
    "jsonpath-ng==1.7.0",
    "psutil==6.0.0",
    "pydantic>=2.10.6",
    "python-dotenv>=1.0.1",
    "pytz>=2025.2",
    "requests==2.31.0",
    "xmltodict==0.14.2",
    "PyYAML==6.0.2",
    "utils",
    "glom==24.11.0",
    "prefect==3.4.4",
    "prefect-docker==0.6.6",
    "lxml==6.0.0"
]

[dependency-groups]
dev = [
    "bandit>=1.7.5",
    "black>=25.1.0",
    "flake8>=6.1.0",
    "isort>=6.0.0",
    "mypy>=1.5.1",
    "pre-commit>=4.1.0",
    "pylint>=3.3.4",
    "ruff>=0.11.10",
]
testing = [
    "coverage>=7.6.12",
    "deepdiff==8.5.0",
    "moto>=5.1.0",
    "pytest-cov>=4.1",
    "pytest-mock>=3.14.0",
    "pytest-order==1.3.0",
    "pytest>=8.3.4",
    "prefect==3.4.4",
    "prefect-docker==0.6.6",
    "lxml==6.0.0"
]

[tool.uv.sources]
utils = { path = "shared_layer", editable = true }

[tool.bandit]
exclude_dirs = ["tests", ".venv", "__pycache__"]

[tool.mypy]
plugins = ["pydantic.mypy", "sqlmypy"]
python_version = "3.12"
follow_imports = "skip"

# Settings shared_layer
[[tool.mypy.overrides]]
module = "shared_layer.*"
strict = true

# Settings tests
[[tool.mypy.overrides]]
module = "tests.*"
strict = false

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true

[tool.ruff]
line-length = 110
indent-width = 4
target-version = "py312"
extend-exclude = [
    ".venv",
    ".pytest_cache",
    ".hypothesis",
    ".vscode",
    "docs",
    "examples",
]

[tool.ruff.lint]
select = [
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "D",   # pydocstyle
    "E",   # pycodestyle (error)
    "F",   # pyflakes
    "I",   # isort
    "ISC", # flake8-implicit-str-concat
    "PIE", # flake8-pie
    "PT",  # flake8-pytest-style
    "RET", # flake8-return
    "S",   # flake8-bandit
    "SIM", # flake8-simplify
    "T20", # flake8-print
    "TD",  # flake8-todos
    "UP",  # pyupgrade
    "W",   # pycodestyle (warning)
]
ignore = [
    "B008",   # FastAPI dependencies
    "UP007",  # Typing as Optional
    "D100",   # Missing docstring in public module
    "D101",   # Missing docstring in public class
    "D102",   # Missing docstring in public method
    "D103",   # Missing docstring in public function
    "D104",   # Missing docstring in public package
    "D105",   # Missing docstring in magic method
    "D106",   # Missing docstring in public nested class
    "D107",   # Missing docstring in __init__
    "D205",   # Blank line required between summary and description
    "RET503", # Missing explicit return at the end of function able to return non-None value
    "SIM102", # Use ternary operator {contents} instead of if-else-block
    "TD002",  # Missing author in TODO
    "TD003",  # Missing issue link on the line following this TODO
]
unfixable = [
    "F601",   # Dictionary key literal {name} repeated
    "F602",   # Dictionary key {name} repeated
    "SIM112", # Use capitalized environment variable {expected} instead of {actual}
]
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.lint.isort]
order-by-type = true
lines-between-types = 1

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.ruff.lint.per-file-ignores]
"tests/*" = [
    "S101", # Use of assert detected
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
docstring-code-format = true

[tool.flake8]
ignore = ["E203", "W503", "F403"]
max-complexity = 10
exclude = [
    "venv",
    "__pycache__",
    ".git",
    "__init__.py",
    ".mypy_cache",
    "pytest_cache",
]

[tool.pytest.ini_options]
addopts = ["--strict-config", "--strict-markers", "--disable-pytest-warnings"]
xfail_strict = true
python_functions = ["test_*"]
testpaths = ["tests"]
python_files = ["test_*.py"]
