stages:
  - triggers

.default-trigger:
  stage: triggers
  trigger:
    include:
      - local: '$TRIGGER_PATH'
    strategy: depend
  rules:
    - if: '$CI_COMMIT_BEFORE_SHA == "0000000000000000000000000000000000000000"'
      when: never
    - if: $CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event" || $CI_COMMIT_TAG =~ /^release\/[0-9]+\.[0-9]+\.[0-9]+-sbx$/
      changes:
        - $TRIGGER_DIR/**/*
        - $TRIGGER_DIR/*
    - when: never
  variables:
    PARENT_PIPELINE_SOURCE: $CI_PIPELINE_SOURCE

# Global rules for changes in akamai_container directory
trigger-akamai-container:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'akamai_container/.akamai-container-ci.yml'
    TRIGGER_DIR: 'akamai_container'

# Global rules for changes in akamai_linode directory
trigger-akamai-linode:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'akamai_linode/.akamai-linode-ci.yml'
    TRIGGER_DIR: 'akamai_linode'

# Global rules for changes in bebanjo directory
trigger-bebanjo:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'bebanjo/.bebanjo-ci.yml'
    TRIGGER_DIR: 'bebanjo'

# Global rules for changes in dot_order directory
trigger-dot-order:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'dot_order/.dot-order-ci.yml'
    TRIGGER_DIR: 'dot_order'

# Global rules for changes in express-lane directory
trigger-express:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'express-lane/.express-ci.yml'
    TRIGGER_DIR: 'express-lane'

# Global rules for changes in gam_connector directory
trigger-gam-connector:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'gam_connector/.gam-connector-ci.yml'
    TRIGGER_DIR: 'gam_connector'

# Global rules for changes in reach directory
trigger-reach:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'reach/.reach-ci.yml'
    TRIGGER_DIR: 'reach'

# Global rules for changes in shared_layer directory
trigger-layers:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'shared_layer/.layers-ci.yml'
    TRIGGER_DIR: 'shared_layer'

# Global rules for changes in wonderland directory
trigger-wonderland:
  extends: .default-trigger
  variables:
    TRIGGER_PATH: 'wonderland/.wonderland-ci.yml'
    TRIGGER_DIR: 'wonderland'
