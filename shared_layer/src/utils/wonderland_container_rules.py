from datetime import datetime as dt
from typing import Any, Optional

from .LoggerService import logger_service
from .RequestsHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .serializers import serialize_json
from .tasks.transforms import update_dynamo_extract


class WonderlandContainerRules:
    def __init__(self, context: dict[str, Any]) -> None:
        self.context = context

        # Extract API configuration from nested structure
        api_config = context.get("api_config", {})
        wonderland_validations = api_config.get("wonderland_validations", {})

        # Status configuration from wonderland_validations
        self.valid_status = wonderland_validations.get("status")
        self.update_status = wonderland_validations.get("update_status")

        # URL configuration from api_config
        self.main_url = str(api_config.get("main_url", ""))
        self.api_url = api_config.get("api_url", "")
        self.table = context.get("tacdev_reach_table", None)

        # Query configuration
        self.query = context.get("query")

        self.request_handler = RequestHandler(self.main_url)

    def __call__(self, previous: Any = None) -> None:
        details, auth_token = previous

        for item in details:
            process_item = self._process_item(item, auth_token)

            if process_item is None:
                continue  # Skip if no data was found

            self._handle_rule(process_item=process_item)

    def _process_item(self, item: dict[str, Any], auth_token: str):
        sidecar_id = item.get("sidecar_id")
        response = self.request_handler.get(
            f"{self.api_url}{sidecar_id}", headers=auth_token
        )
        if not response:
            logger_service.warning("No data found for Sidecar ID: %s", sidecar_id)
            return

        data = serialize_json(response)
        sidecar_details = data.get("sidecar_details", {})

        return sidecar_details

    def _handle_rule(self, process_item: dict[str, Any]):
        if process_item is None:
            logger_service.error("Received None in _handle_rule; skipping.")
            return

        sidecar_status = process_item.get("status")
        sidecar_id = process_item.get("uuid")
        timestamp = dt.now().isoformat()

        if sidecar_status == self.valid_status:
            logger_service.info(
                "wonderland status COMPLETED and update datetime: %s, with sidecarID: %s",
                sidecar_id,
                timestamp,
            )

            key = {"sidecar_id": sidecar_id}
            value = {
                "updated_at": timestamp,
                "status": self.update_status,
            }

            update_dynamo_extract(self.table, key, value)

            logger_service.info(
                "wonderland status update datetime completed: %s, with sidecarID: %s",
                timestamp,
                sidecar_id,
            )
        else:
            # If not status of sidecar is Completed
            key = {"sidecar_id": sidecar_id}
            value = {
                "updated_at": timestamp,
            }

            update_dynamo_extract(self.table, key, value)
            logger_service.warning(
                "for sidecar id: %s the status: %s is not equal to COMPLETED",
                sidecar_id,
                sidecar_status,
            )
