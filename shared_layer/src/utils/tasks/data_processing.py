import json
from typing import Any, Dict, Optional, Union

import xmltodict

from ..rascl.parse_normal_series_from_MediaHub_JSON import parse_normal_series
from ..rascl.reach_series_toolbox import load_to_reach
from ..rascl.reach_series_toolbox import main as reach_main
from ..schemas.payloads import BebanjoPayloadSchema
from ..token_service import ReachTokenService


def convert_to_dict(
    prev: Any, source_format: str, global_variables: Optional[Dict[str, Any]] = None
) -> Any:
    """Convert data from a specific format to a Python dictionary.

    Args:
        prev: The data to be converted.
        source_format: The format of the input data ('xml', 'json', or other).
        global_variables: Additional variables that may be used during conversion.

    Returns:
        dict: The converted data as a dictionary if source_format is 'xml' or 'json',
              otherwise returns the original data.
    """
    if source_format == "xml":
        return xmltodict.parse(prev)
    elif source_format == "json":
        return json.loads(prev)
    return prev


def convert_from_dict(
    prev: Any, target_format: str, global_variables: Optional[Dict[str, Any]] = None
) -> Any:
    """Convert a Python dictionary to a specific format.

    Args:
        prev: The dictionary data to be converted.
        target_format: The format to convert to ('xml', 'json', or other).
        global_variables: Additional variables that may be used during conversion.

    Returns:
        Union[str, Any]: The converted data as a string if target_format is 'xml' or 'json',
                        otherwise returns the original data.
    """
    if target_format == "xml":
        return xmltodict.unparse(prev, pretty=True)
    elif target_format == "json":
        return json.dumps(prev)
    return prev


def download_s3_object(
    prev: Optional[dict] = None, key: str = "", connector: Any = None, bucket: str = ""
) -> bytes:
    """Download an object from an S3 bucket.

    Args:
        prev: Previous task's output. Not used in this function but kept for task chaining.
        key: The S3 object key to download.
        connector: The function or method to execute the S3 operation.
        bucket: The name of the S3 bucket.

    Returns:
        bytes: The downloaded object's data.
    """
    task_def: dict[str, Any] = {
        "extract": {
            "task": "s3_extract",
            "params": {
                "key": key,
                "bucket": bucket,
            },
        }
    }
    result = connector(task_def)
    return result


def parse_normal_series_from_bebanjo(
    prev: dict[str, Any], settings: Optional[dict[str, Any]] = None
) -> dict[str, Any]:
    """Validate Bebanjo JSON and build ADI JSON via RASCL main.

    This function expects the output of TaskDeserializeData (a Python dict
    produced from the S3 JSON). It validates the payload against
    BebanjoPayloadSchema and, if valid, serializes the model using
    PascalCase keys (via serialization_alias) and invokes the RASCL ``main``
    method to construct the ``adi_json``.

    Args:
        prev: The deserialized Bebanjo JSON payload.
        settings: Optional configuration context containing a ``secrets``
            mapping required by RASCL (auth/bearer/gracenote).

    Returns:
        dict[str, Any]: The constructed ADI JSON.

    Raises:
        pydantic.ValidationError: If the payload does not match the schema.
        Exception: Any exception propagated from the RASCL parser.
    """
    if not isinstance(prev, dict):
        raise TypeError("Expected 'prev' to be a dict with the Bebanjo payload")
    
    model: BebanjoPayloadSchema = BebanjoPayloadSchema.model_validate(prev)
    # Serialize with PascalCase keys expected by RASCL
    # serialized_payload = model.model_dump(by_alias=True, exclude_none=True)
    json_root = {
        "AirDate": "2021-12-25T08:00:00Z",
        "Actors":"Mark, Cuban",
        "Categories": [
            {
            "NLDTypesGroup": [
                "C7"
            ],
            "Type": "HD",
            "CTS": [
                ""
            ]
            }
        ],
        "ContentLabels": [
        ],
        "FXMRetro20CF": None,  # verify if blank could be a choice
        "Genres": [
            "Entertainment"
        ],
        "MaterialId": "Shark 12-24 HD",
        "Name": "Shark Tank",
        "Network": "ABC",
        "NLDTypes": [
            {
            "StartDate": "2021-12-25T08:00:00Z",
            "Type": "C7",
            "Revision": 0,
            "EndDate": "2022-01-01T07:59:00Z"
            }
        ],
        "OriginalContent20CF": None, # verify if blank could be a choice
        "Rating": "TV-PG",
        "SummaryShort": "Holiday cheer heads into the Tank when an entrepreneur from Charlotte, North",
        "SeasonNumber": "13",
        "ShowType": "Series",
        "ShowName": "Shark Tank",
        "TitleFullSd": "Shark Tank",
        "EpisodeNumber": "1308",
        "Year": "2021"
    }

    json_root_movie = {
        "AirDate": "2021-12-25T08:00:00Z",
        "Actors":"Tom Hanks (  ), Helen Hunt (  ), Paul Sanchez (  )",
        "Categories": [
            {
            "NLDTypesGroup": [
                "VOD4"
            ],
            "Type": "HD",
            "CTS": [
                ""
            ]
            }
        ],
        "ContentLabels": [
        ],
        "FXMRetro20CF": None,  # verify if blank could be a choice
        "Genres": [
            "Movie"
        ],
        "MaterialId": "NONE",
        "Name": "CAST AWAY",
        "Network": "FF",
        "NLDTypes": [
            {
            "StartDate": "2021-11-09T08:00:00Z",
            "Type": "VOD4",
            "Revision": 0,
            "EndDate": "2021-11-16T07:59:00Z"
            }
        ],
        "OriginalContent20CF": None, # verify if blank could be a choice
        "Rating": "TV-14",
        "SummaryShort": "A FedEx executive must transform himself physically and emotionally to survive a crash landing on a deserted island.",
        "SeasonNumber": None,
        "ShowType": "Movie",
        "ShowName": "CAST AWAY",
        "TitleFullSd": "CAST AWAY",
        "EpisodeNumber": "1308",
        "Year": "2021"
    }


   

    # Build ADI JSON using the serialized PascalCase payload
    return parse_normal_series(json_root, settings=settings)


def upload_to_s3(prev: Any, key: str, connector: Any, bucket: str) -> bytes:
    """Upload data to an S3 bucket.

    Args:
        prev: The data to upload to S3.
        key: The S3 object key where the data will be stored.
        connector: The function or method to execute the S3 operation.
        bucket: The name of the S3 bucket.

    Returns:
        bytes: Response from the S3 upload operation.
    """
    task_def: dict[str, Any] = {
        "load": {
            "task": "s3_upload",
            "params": {
                "Key": key,
                "Bucket": bucket,
                "Body": prev,
            },
        }
    }
    result = connector(task_def)
    return result


def retrieve_dynamodb_item(
    table_name: str, key: Any, method: str, connector: Any
) -> dict:
    """Retrieve an item from a DynamoDB table.

    Args:
        table_name: The name of the DynamoDB table.
        key: The key identifying the item to retrieve.
        method: The method to use for retrieval (e.g., 'get_item', 'query').
        connector: The function or method to execute the DynamoDB operation.

    Returns:
        dict: The retrieved item or query result from DynamoDB.
    """
    task_def: dict[str, Any] = {
        "extract": {
            "task": "dynamo_extract",
            "params": {"table": table_name, "key": key, "method": method},
        }
    }

    result = connector(task_def)
    return result


def adi_json_load_to_reach(
    prev: dict[str, Any], 
    settings: Optional[dict[str, Any]] = None,
    artwork_path: str = "",
    small_artwork_path: str = ""
) -> dict[str, Any]:
    """Load content to Reach Engine using the RASCL main function.

    This function takes the result from the previous task (typically the ADI JSON
    from parse_normal_series_from_bebanjo) and passes it to the Reach Engine
    main function to create or upload a reach package.

    Args:
        prev: The result from the previous task (ADI JSON content).
        settings: Optional configuration context containing secrets and reach configuration.
        artwork_path: Path to large artwork file (optional).
        small_artwork_path: Path to small artwork file (optional).

    Returns:
        dict[str, Any]: The result from the Reach Engine main function.

    Raises:
        Exception: Any exception propagated from the Reach Engine main function or token generation.
    """
    if not isinstance(prev, dict):
        raise TypeError("Expected 'prev' to be a dict with the content JSON")
    
    if not settings:
        raise ValueError("Settings are required for Reach Engine configuration")
    
    # Determine environment
    env = settings.get('env', 'sbx')
    
    # Call the load_to_reach function from reach_series_toolbox with all required parameters
    result = load_to_reach(
        data=prev,
        artwork=artwork_path if artwork_path else None,
        smallartwork=small_artwork_path if small_artwork_path else None,
        vodartwork=None,  # No VOD artwork parameter provided
        settings=settings,
        env=env
    )
    
    return {"result": result, "status": "success"}
