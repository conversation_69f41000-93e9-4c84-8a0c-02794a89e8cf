from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.schemas.dynamodb.express_lane import ExpressLaneConfig
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings_manager import SettingsManagerService


def config_variables(event: dict) -> dict:
    """
    Initializes all the services and configurations and runs the flow
    according to the configuration obtained.
    """

    logger_service.info(
        "************ Config Variables / Running Lambda Express Lane ************"
    )

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    event_settings = settings_service.initialize("express-lane")
    config: ExpressLaneConfig = event_settings.get_config()  # type: ignore
    status_table_name = event_settings.get_status_table()
    secrets_manager = config.secrets_manager

    secrets_manager_utils = SecretsManagerUtils()
    secrets_managerUtils = secrets_manager_utils.get_secret_value(secrets_manager)

    records = event.get("Records")

    s3_event_info = records[0]["s3"]
    file_name_from_s3 = s3_event_info.get("object", {}).get("key")
    bucket_name = s3_event_info.get("bucket", {}).get("name")

    rules = event_settings.configuration_rules.rules
    config_rules = rules.get("config_rules")

    global_variables = {
        "id": file_name_from_s3,
        "status_table_name": status_table_name,
        "event_project_name": event_settings.event_project_name,
        "landing_bucket": bucket_name,
        "destination_bucket": config.destination_bucket,
        "output_filename": config.output_filename,
        "file_name_from_s3": file_name_from_s3,
        "secrets_manager": secrets_managerUtils,
        "timezone": config.timezone,
        "event_settings_rules": rules,
        "config_rules": config_rules,
        "prefect": config.prefect,
    }

    return global_variables
