from typing import Any

from .extract import extract
from .flow_manager import GenericTask, TaskProtocol
from .LoggerService import logger_service
from .RequestsHandler import create_auth_token
from .schemas.lambdas import EventSchema
from .schemas.payloads import WonderlandSidecarSchema
from .tasks.data_processing import (
    adi_json_load_to_reach,
    convert_to_dict,
    download_s3_object,
    parse_normal_series_from_bebanjo,
    retrieve_dynamodb_item,
    upload_to_s3,
)
from .tasks.external import send_message_to_sqs, upload_file_to_linode
from .tasks.package_metadata import (
    fetch_reach_package_metadata,
    update_reach_package_metadata,
)
from .tasks.transforms import (
    convert_json_to_yaml,
    update_dynamo_extract,
    update_dynamo_record_status,
    update_wonderland_status_in_dynamo,
)
from .tasks.validations import (
    validate_and_update_dynamodb_record_status,
    validate_data_against_pydantic_schema,
)
from .wonderland_container_rules import WonderlandContainerRules


class TaskDeserializeData(GenericTask):
    """
    Task to deserialize JSON data from TaskExtractData result.
    Expects the previous task's result to be a string containing JSON data.
    """

    def perform_task(self, previous: Any = None) -> dict:
        """
        Deserialize the JSON data from the previous task's result.

        Args:
            previous: The result from the previous task (TaskExtractData).

        Returns:
            The deserialized JSON data as a Python dictionary.

        Raises:
            json.JSONDecodeError: If the input is not valid JSON.
            TypeError: If the input is not a string or bytes-like object.
        """
        if previous is None:
            raise ValueError("No data provided to deserialize")

        return convert_to_dict(previous, "json")


class TaskExtractData(GenericTask):
    task = download_s3_object

    def prepare(
        self,
        event: EventSchema,
        context: dict[str, Any],
    ) -> TaskProtocol:
        self.kwargs = {
            "key": event.get_file_name_from_s3(),
            "connector": context.get("connector"),
            "bucket": event.get_bucket_name(),
        }
        return self


class TaskLinodeLoad(GenericTask):
    task = upload_file_to_linode

    def prepare(
        self,
        event: EventSchema,
        context: dict[str, Any],
    ) -> TaskProtocol:
        file_name_from_s3 = event.get_file_name_from_s3()
        config = context.get("config", {})
        prefix = config.get("filename_prefix")

        self.kwargs = {
            "secret_key": config.get("secret_key"),
            "filename": f"{prefix}{file_name_from_s3}",
        }
        return self


class TaskSendMessageToSQS(GenericTask):
    task = send_message_to_sqs

    def prepare(self, event: EventSchema, context: dict[str, Any]) -> "TaskProtocol":
        self.kwargs = {"global_variables": context}
        return self

    def perform_task(self, previous: Any = None) -> Any:
        self.kwargs["sqs_messages"] = previous
        return super().perform_task(previous)


class TaskConvertJsonToYaml(GenericTask):
    task = convert_json_to_yaml


class TaskLoadData(GenericTask):
    task = upload_to_s3

    def prepare(self, event: EventSchema, context: dict[str, Any]) -> TaskProtocol:
        file_name_from_s3 = event.get_file_name_from_s3()
        file_name = file_name_from_s3.split("/")[-1].replace(".json", ".yml")
        destination_folder = context["destination_folder"]
        output_filename = f"{destination_folder}/{file_name}"

        self.kwargs = {
            "key": output_filename,
            "connector": context.get("load_data_connector"),
            "bucket": event.get_bucket_name(),
        }

        return self


class TaskUpdateWonderlandStatus(GenericTask):
    task = update_wonderland_status_in_dynamo


class TaskExtractObj(GenericTask):
    task = retrieve_dynamodb_item

    def prepare(
        self,
        _: EventSchema,
        global_variables: dict[str, Any],
    ) -> TaskProtocol:
        self.kwargs = {
            "table_name": global_variables.get("tacdev_reach_table"),
            "key": global_variables.get("query"),
            "method": global_variables.get("method"),
            "connector": extract,
        }
        return self


class TaskValidateRecordUpdateStatus(GenericTask):
    task = validate_and_update_dynamodb_record_status

    def prepare(
        self,
        _: EventSchema,
        context: dict[str, Any],
    ):
        self.record = context.get("record", {})
        self.table_name = context["table_name"]
        self.key = context["key"]
        self.sidecar = context["sidecar"]
        self.kwargs = {
            "record": self.record,
            "table_name": self.table_name,
            "key": self.key,
            "sidecar": self.sidecar,
        }
        return self


class TaskGetPackageMetadata(GenericTask):
    task = fetch_reach_package_metadata

    def prepare(
        self,
        _: EventSchema,
        global_variables: dict[str, Any],
    ) -> TaskProtocol:
        self.record = global_variables.get("dynamodb_stream")
        self.token = global_variables.get("token")
        self.url = global_variables.get("url")
        self.kwargs = {
            "record": self.record,
            "token": self.token,
            "url": self.url,
        }

        return self


class TaskUpdatePackageMetadata(GenericTask):
    def prepare(
        self,
        _: EventSchema,
        global_variables: dict[str, Any],
    ) -> TaskProtocol:
        self.record = global_variables.get("dynamodb_stream")
        self.token = global_variables.get("token")
        self.url = global_variables.get("url")
        self.wonderland_delivery_status_mapping = global_variables.get("wonderland_delivery_status_mapping")
        return self

    def perform_task(self, previous=None):
        success = update_reach_package_metadata(
            self.record, self.token, self.url, previous, self.wonderland_delivery_status_mapping
        )

        status_update = "wl_reach_completed" if success else "Error"

        # if not update_dynamo_record_status(
        #     self.table_name, self.key, status_update, self.sidecar
        # ):
        #     logger_service.error("Failed to update status to %s", status_update)
        #     raise Exception(f"Failed to update status to {status_update}")

        return status_update


class TaskCreateAuthToken(GenericTask):
    def prepare(
        self,
        _: EventSchema,
        global_variables: dict[str, Any],
    ) -> TaskProtocol:
        self.token_url = global_variables.get("api_config").get("token_url")
        self.secrets = global_variables.get("api_config").get("secrets")
        return self

    def perform_task(self, previous=None):
        auth_token = create_auth_token(
            self.token_url,
            self.secrets,
        )

        return previous, auth_token


class TaskFetchWonderlandContainerDetailsTask(GenericTask):
    def prepare(self, _: EventSchema, context: dict[str, Any]) -> "TaskProtocol":
        self.wonderland_rules = WonderlandContainerRules(context=context)
        return self

    def perform_task(self, previous: Any = None) -> Any:
        self.wonderland_rules.__call__(previous=previous)


class TaskValidateWonderlandSidecar(GenericTask):
    task = validate_data_against_pydantic_schema

    def prepare(self, _: EventSchema, context: dict[str, Any]) -> "TaskProtocol":
        """
        Prepares the task by setting the validation schema in kwargs.

        Args:
            _: The event schema (unused).
            context: The context dictionary (unused in this specific prepare).

        Returns:
            The prepared task instance.
        """
        self.kwargs = {"model_schema": WonderlandSidecarSchema}
        return self


class TaskParseNormalSeries(GenericTask):
    """Validate Bebanjo payload and build ADI JSON using RASCL parser."""

    task = parse_normal_series_from_bebanjo

    def prepare(self, event: EventSchema, context: dict[str, Any]) -> TaskProtocol:
        # Pass configuration (including secrets) to the processing function
        self.kwargs = {"settings": context}
        return self


class TaskLoadToReach(GenericTask):
    """Load content to Reach Engine using the RASCL main function."""

    task = adi_json_load_to_reach

    def prepare(
        self,
        event: EventSchema,
        context: dict[str, Any],
    ) -> TaskProtocol:
        """
        Prepare the task with Reach Engine configuration.

        Args:
            event: The event schema
            context: Global variables containing reach engine configuration

        Returns:
            The prepared task instance
        """
        # Extract reach engine configuration from context
        reach_config = context.get("secrets", {})

        self.kwargs = {
            "settings": reach_config
        }
        return self

