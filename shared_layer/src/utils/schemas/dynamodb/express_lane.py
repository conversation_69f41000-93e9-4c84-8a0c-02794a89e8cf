from typing import Dict, List, Literal, Optional, Tuple

from pydantic import BaseModel


class PrefectConfig(BaseModel):
    base_url: str
    prefect_api_deployment: str
    prefect_api_flow_run: str
    flow_id: str
    deployment_id: str


class ExpressLaneConfig(BaseModel):
    project_type: Literal["express-lane"]
    output_filename: Optional[str] = ""
    landing_bucket: str
    secrets_manager: str
    status_table_name: str
    destination_bucket: str
    timezone: Optional[str] = "US/Pacific"
    prefect: Optional[PrefectConfig] = {}


class RulesIsHD(BaseModel):
    hd_path: str


class RulesDataPath(BaseModel):
    name_value: str
    value: str
    app_data_path: str
    app_data_value: str
    category_value: str
    app_value: str


class RulesMetadata(BaseModel):
    network_name_path: str
    metadata_network_path: str
    metadata_movie_path: str
    metadata_season_path: str
    metadata_path: str
    affiliate_list_path: str
    affiliate_path: str
    adi_path: str
    ams_path: str


class ConfigRules(BaseModel):
    affiliate_connections: List
    is_hd: RulesIsHD
    data_path: RulesDataPath
    metadata_path: RulesMetadata


class RemoveAttributes(BaseModel):
    filter_key: str
    keys: List[str]
    path: str


class ReplaceAttribute(BaseModel):
    key_value_filter: str
    value_filter: str


class ReplaceAttributeBy(BaseModel):
    filter_by: str
    key_value_filter_by: str
    value_filter_by: str


class UpdateAttribute(BaseModel):
    key_value_filter: str
    value_filter: str
    value_update: str


class SkipContent(BaseModel):
    validation: Optional[bool] = False
    values_skip: Optional[List[str]] = []


class LimitLength(BaseModel):
    key_filter: str
    value_filter: str
    length: int
    validation: bool


class AppliedRules(BaseModel):
    skip_affiliate_rules: bool
    filter_category_rule: List
    apply_category_rules: Dict
    replace_attributes_by: Dict
    replace_attributes: Dict
    split_rules: Dict


class ExpressLaneRulesConfig(BaseModel):
    delivery_name: str
    faspex: bool
    has_HD: bool
    has_SD: bool
    HD_movies_category: str
    HD_series_category: str
    SD_movies_category: str
    SD_series_category: str
    remove_attributes: Optional[RemoveAttributes] = None
    replace_attributes: List[ReplaceAttribute] = []
    replace_attributes_by: List[ReplaceAttributeBy] = []
    skip_content: Optional[SkipContent] = None
    tar: bool
    update_attributes: List[UpdateAttribute] = []
    limit_length: Optional[LimitLength] = None
    applied_rules: Optional[AppliedRules] = None

    @classmethod
    def parse_rules_config(
        cls, data: dict
    ) -> Tuple[Dict[str, "ExpressLaneRulesConfig"], Optional[ConfigRules]]:
        affiliate_config_rules: Dict[str, ExpressLaneRulesConfig] = {}
        config_rules = None

        for key, value in data.items():
            if key == "config_rules":
                config_rules = ConfigRules(**value)
            else:
                if value.get("remove_attributes") == {}:
                    value["remove_attributes"] = None
                if value.get("skip_content") == {}:
                    value["skip_content"] = None
                if value.get("limit_length") == {}:
                    value["limit_length"] = None

                affiliate_config_rules[key] = cls(**value)

        return affiliate_config_rules, config_rules
