from datetime import datetime
from typing import Any, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


class WonderlandSidecarSchema(BaseModel):
    """Schema for Wonderland sidecar JSON payload."""

    model_config = ConfigDict(extra="ignore")

    version: int
    sidecar_id: str
    asset_version: str
    asset_format: Literal["single_file", "multi_file"]
    asset_id: str
    reach_package_id: str


class BebanjoPayloadSchema(BaseModel):
    """Schema for Bebanjo payload from S3 bucket.
    
    Validates all fields from the Bebanjo JSON payload.
    Only 'network' field is required, all others are optional.
    Uses serialization_alias to output PascalCase keys.
    """

    model_config = ConfigDict(extra="ignore", str_strip_whitespace=True)

    # Core identifiers - Only network is required
    network: str = Field(
        alias="network", 
        serialization_alias="Network", 
        description="Network identifier (required)"
    )
    
    # Optional core fields
    event_id: Optional[int] = Field(
        default=None, 
        alias="eventId", 
        serialization_alias="EventId", 
        description="Event identifier"
    )
    transaction_time: Optional[str] = Field(
        default=None, 
        alias="transactionTime", 
        serialization_alias="TransactionTime", 
        description="Transaction timestamp"
    )
    unique_id: Optional[str] = Field(
        default=None, 
        alias="uniqueId", 
        serialization_alias="UniqueId", 
        description="Unique identifier for the content"
    )
    traffic_code: Optional[str] = Field(
        default=None, 
        alias="trafficCode", 
        serialization_alias="TrafficCode", 
        description="Traffic code identifier"
    )
    change_type: Optional[str] = Field(
        default=None, 
        alias="changeType", 
        serialization_alias="ChangeType", 
        description="Type of change (A, U, D)"
    )
    
    # Window dates - All optional
    vod4_window_start: Optional[str] = Field(
        default=None, 
        alias="vod4WindowStart", 
        serialization_alias="Vod4WindowStart", 
        description="VOD4 window start date"
    )
    vod4_window_end: Optional[str] = Field(
        default=None, 
        alias="vod4WindowEnd", 
        serialization_alias="Vod4WindowEnd", 
        description="VOD4 window end date"
    )
    vod8_window_start: Optional[str] = Field(
        default=None, 
        alias="vod8WindowStart", 
        serialization_alias="Vod8WindowStart", 
        description="VOD8 window start date"
    )
    vod8_window_end: Optional[str] = Field(
        default=None, 
        alias="vod8WindowEnd", 
        serialization_alias="Vod8WindowEnd", 
        description="VOD8 window end date"
    )
    c3_window_start: Optional[str] = Field(
        default=None, 
        alias="c3WindowStart", 
        serialization_alias="C3WindowStart", 
        description="C3 window start date"
    )
    c3_window_end: Optional[str] = Field(
        default=None, 
        alias="c3WindowEnd", 
        serialization_alias="C3WindowEnd", 
        description="C3 window end date"
    )
    c7_window_start: Optional[str] = Field(
        default=None, 
        alias="c7WindowStart", 
        serialization_alias="C7WindowStart", 
        description="C7 window start date"
    )
    c7_window_end: Optional[str] = Field(
        default=None, 
        alias="c7WindowEnd", 
        serialization_alias="C7WindowEnd", 
        description="C7 window end date"
    )
    clean_start: Optional[str] = Field(
        default=None, 
        alias="cleanStart", 
        serialization_alias="CleanStart", 
        description="Clean window start date"
    )
    clean_end: Optional[str] = Field(
        default=None, 
        alias="cleanEnd", 
        serialization_alias="CleanEnd", 
        description="Clean window end date"
    )
    
    # Platform and provider information
    platform_name: Optional[str] = Field(
        default=None, 
        alias="platformName", 
        serialization_alias="PlatformName", 
        description="Platform name"
    )
    provider: Optional[str] = Field(
        default=None, 
        alias="provider", 
        serialization_alias="Provider", 
        description="Content provider"
    )
    
    # Title information
    vod_title: Optional[str] = Field(
        default=None, 
        alias="vodTitle", 
        serialization_alias="VodTitle", 
        description="VOD title"
    )
    vod_short_title: Optional[str] = Field(
        default=None, 
        alias="vodShortTitle", 
        serialization_alias="VodShortTitle", 
        description="VOD short title"
    )
    episode_vod_title: Optional[str] = Field(
        default=None, 
        alias="episodeVodTitle", 
        serialization_alias="EpisodeVodTitle", 
        description="Episode VOD title"
    )
    episode_vod_short_title: Optional[str] = Field(
        default=None, 
        alias="episodeVodShortTitle", 
        serialization_alias="EpisodeVodShortTitle", 
        description="Episode VOD short title"
    )
    episode_name: Optional[str] = Field(
        default=None, 
        alias="episodeName", 
        serialization_alias="EpisodeName", 
        description="Episode name"
    )
    show_title: Optional[str] = Field(
        default=None, 
        alias="showTitle", 
        serialization_alias="ShowTitle", 
        description="Show title"
    )
    
    # Episode/Season information
    episode_id: Optional[str] = Field(
        default=None, 
        alias="episodeID", 
        serialization_alias="EpisodeId", 
        description="Episode identifier"
    )
    season_number: Optional[int] = Field(
        default=None, 
        alias="seasonNumber", 
        serialization_alias="SeasonNumber", 
        description="Season number"
    )
    episode_number: Optional[int] = Field(
        default=None, 
        alias="episodeNumber", 
        serialization_alias="EpisodeNumber", 
        description="Episode number"
    )
    show_type: Optional[str] = Field(
        default=None, 
        alias="showType", 
        serialization_alias="ShowType", 
        description="Show type (e.g., TVShow)"
    )
    
    # Content metadata
    summary_short: Optional[str] = Field(
        default=None, 
        alias="summaryShort", 
        serialization_alias="SummaryShort", 
        description="Short summary"
    )
    one_line_description: Optional[str] = Field(
        default=None, 
        alias="oneLineDescription", 
        serialization_alias="OneLineDescription", 
        description="One line description"
    )
    rating: Optional[str] = Field(
        default=None, 
        alias="rating", 
        serialization_alias="Rating", 
        description="Content rating"
    )
    episode_rating: Optional[str] = Field(
        default=None, 
        alias="episodeRating", 
        serialization_alias="EpisodeRating", 
        description="Episode rating"
    )
    movie_rating: Optional[str] = Field(
        default=None, 
        alias="movieRating", 
        serialization_alias="MovieRating", 
        description="Movie rating"
    )
    display_run_time: Optional[str] = Field(
        default=None, 
        alias="displayRunTime", 
        serialization_alias="DisplayRunTime", 
        description="Display runtime"
    )
    year: Optional[int] = Field(
        default=None, 
        alias="year", 
        serialization_alias="Year", 
        description="Release year"
    )
    
    # Categories and genres
    cmc_categories: Optional[List[str]] = Field(
        default=None, 
        alias="cmcCategories", 
        serialization_alias="CmcCategories", 
        description="CMC categories"
    )
    genres: Optional[str] = Field(
        default=None, 
        alias="genres", 
        serialization_alias="Genres", 
        description="Content genres"
    )
    actors: Optional[str] = Field(
        default=None, 
        alias="actors", 
        serialization_alias="Actors", 
        description="Actors list"
    )
    
    # Technical specifications
    closed_captioning: Optional[str] = Field(
        default=None, 
        alias="closedCaptioning", 
        serialization_alias="ClosedCaptioning", 
        description="Closed captioning availability"
    )
    hd_content: Optional[str] = Field(
        default=None, 
        alias="hdContent", 
        serialization_alias="HdContent", 
        description="HD content flag"
    )
    audio_type: Optional[str] = Field(
        default=None, 
        alias="audioType", 
        serialization_alias="AudioType", 
        description="Audio type (e.g., Dolby 5.1)"
    )
    languages: Optional[str] = Field(
        default=None, 
        alias="languages", 
        serialization_alias="Languages", 
        description="Available languages"
    )
    trick_modes_restricted: Optional[str] = Field(
        default=None, 
        alias="trickModesRestricted", 
        serialization_alias="TrickModesRestricted", 
        description="Trick modes restrictions"
    )
    
    # External identifiers
    radar_product_id: Optional[str] = Field(
        default=None, 
        alias="radarProductId", 
        serialization_alias="RadarProductId", 
        description="Radar product identifier"
    )
    provider_id: Optional[str] = Field(
        default=None, 
        alias="providerId", 
        serialization_alias="ProviderId", 
        description="Provider identifier"
    )
    series_id: Optional[str] = Field(
        default=None, 
        alias="seriesId", 
        serialization_alias="SeriesId", 
        description="Series identifier"
    )
    series_name: Optional[str] = Field(
        default=None, 
        alias="seriesName", 
        serialization_alias="SeriesName", 
        description="Series name"
    )
    show_code: Optional[str] = Field(
        default=None, 
        alias="showCode", 
        serialization_alias="ShowCode", 
        description="Show code"
    )
    
    # Licensing and dates
    licensing_window_start: Optional[str] = Field(
        default=None, 
        alias="licensingWindowStart", 
        serialization_alias="LicensingWindowStart", 
        description="Licensing window start date"
    )
    licensing_window_end: Optional[str] = Field(
        default=None, 
        alias="licensingWindowEnd", 
        serialization_alias="LicensingWindowEnd", 
        description="Licensing window end date"
    )
    air_date: Optional[str] = Field(
        default=None, 
        alias="airDate", 
        serialization_alias="AirDate", 
        description="Air date"
    )
    create_date: Optional[str] = Field(
        default=None, 
        alias="createDate", 
        serialization_alias="CreateDate", 
        description="Creation date"
    )
    original_airdate: Optional[str] = Field(
        default=None, 
        alias="originalAirdate", 
        serialization_alias="OriginalAirdate", 
        description="Original air date"
    )
    published_date_time: Optional[str] = Field(
        default=None, 
        alias="publishedDateTime", 
        serialization_alias="PublishedDateTime", 
        description="Published date time"
    )
    episode_original_air_date: Optional[bool] = Field(
        default=None, 
        alias="episodeOriginalAirDate", 
        serialization_alias="EpisodeOriginalAirDate", 
        description="Episode original air date flag"
    )
    
    # Asset information
    asset_type: Optional[str] = Field(
        default=None, 
        alias="assetType", 
        serialization_alias="AssetType", 
        description="Asset type"
    )
    asset_name: Optional[str] = Field(
        default=None, 
        alias="assetName", 
        serialization_alias="AssetName", 
        description="Asset name"
    )
    xml_file_name: Optional[str] = Field(
        default=None, 
        alias="xmlFileName", 
        serialization_alias="XmlFileName", 
        description="XML file name"
    )
    video_file_name: Optional[str] = Field(
        default=None, 
        alias="videoFileName", 
        serialization_alias="VideoFileName", 
        description="Video file name"
    )
    s3_file_location: Optional[str] = Field(
        default=None, 
        alias="s3FileLocation", 
        serialization_alias="S3FileLocation", 
        description="S3 file location"
    )
    
    # Package assets
    asset_id_package: Optional[str] = Field(
        default=None, 
        alias="assetIdPackage", 
        serialization_alias="AssetIdPackage", 
        description="Asset ID package"
    )
    asset_name_package: Optional[str] = Field(
        default=None, 
        alias="assetNamePackage", 
        serialization_alias="AssetNamePackage", 
        description="Asset name package"
    )
    description_package: Optional[str] = Field(
        default=None, 
        alias="descriptionPackage", 
        serialization_alias="DescriptionPackage", 
        description="Package description"
    )
    
    # Title assets
    asset_id_title: Optional[str] = Field(
        default=None, 
        alias="assetIdTitle", 
        serialization_alias="AssetIdTitle", 
        description="Asset ID title"
    )
    asset_name_title: Optional[str] = Field(
        default=None, 
        alias="assetNameTitle", 
        serialization_alias="AssetNameTitle", 
        description="Asset name title"
    )
    description_title: Optional[str] = Field(
        default=None, 
        alias="descriptionTitle", 
        serialization_alias="DescriptionTitle", 
        description="Title description"
    )
    title_brief: Optional[str] = Field(
        default=None, 
        alias="titleBrief", 
        serialization_alias="TitleBrief", 
        description="Title brief"
    )
    title_sort_name: Optional[str] = Field(
        default=None, 
        alias="titleSortName", 
        serialization_alias="TitleSortName", 
        description="Title sort name"
    )
    
    # Movie assets
    asset_id_movie: Optional[str] = Field(
        default=None, 
        alias="assetIdMovie", 
        serialization_alias="AssetIdMovie", 
        description="Asset ID movie"
    )
    asset_name_movie: Optional[str] = Field(
        default=None, 
        alias="assetNameMovie", 
        serialization_alias="AssetNameMovie", 
        description="Asset name movie"
    )
    description_movie: Optional[str] = Field(
        default=None, 
        alias="descriptionMovie", 
        serialization_alias="DescriptionMovie", 
        description="Movie description"
    )
    
    # Poster assets
    asset_id_post: Optional[str] = Field(
        default=None, 
        alias="assetIdPost", 
        serialization_alias="AssetIdPost", 
        description="Asset ID poster"
    )
    asset_name_post: Optional[str] = Field(
        default=None, 
        alias="assetNamePost", 
        serialization_alias="AssetNamePost", 
        description="Asset name poster"
    )
    description_post: Optional[str] = Field(
        default=None, 
        alias="descriptionPost", 
        serialization_alias="DescriptionPost", 
        description="Poster description"
    )
    
    # Artwork information
    artworks_url: Optional[str] = Field(
        default=None, 
        alias="artworksUrl", 
        serialization_alias="ArtworksUrl", 
        description="Artworks URL"
    )
    artworks_type: Optional[str] = Field(
        default=None, 
        alias="artworksType", 
        serialization_alias="ArtworksType", 
        description="Artworks type"
    )
    artworks_file_name: Optional[str] = Field(
        default=None, 
        alias="artworksFileName", 
        serialization_alias="ArtworksFileName", 
        description="Artworks file name"
    )
    
    # Content file information
    content_file_size_image: Optional[int] = Field(
        default=None, 
        alias="contentFileSizeImage", 
        serialization_alias="ContentFileSizeImage", 
        description="Content file size for image"
    )
    content_check_sum_image: Optional[str] = Field(
        default=None, 
        alias="contentCheckSumImage", 
        serialization_alias="ContentCheckSumImage", 
        description="Content checksum for image"
    )
    
    # Additional metadata
    schedule_materials: Optional[str] = Field(
        default=None, 
        alias="scheduleMaterials", 
        serialization_alias="ScheduleMaterials", 
        description="Schedule materials JSON string"
    )
    season_premiere: Optional[str] = Field(
        default=None, 
        alias="seasonPremiere", 
        serialization_alias="SeasonPremiere", 
        description="Season premiere flag"
    )
    season_finale: Optional[str] = Field(
        default=None, 
        alias="seasonFinale", 
        serialization_alias="SeasonFinale", 
        description="Season finale flag"
    )
    country_of_origin: Optional[str] = Field(
        default=None, 
        alias="countryOfOrigin", 
        serialization_alias="CountryOfOrigin", 
        description="Country of origin"
    )
    authority: Optional[str] = Field(
        default=None, 
        alias="authority", 
        serialization_alias="Authority", 
        description="Content authority"
    )
    content_rating_descriptors: Optional[str] = Field(
        default=None, 
        alias="contentRatingDescriptors", 
        serialization_alias="ContentRatingDescriptors", 
        description="Content rating descriptors"
    )
    
    # Business information
    product: Optional[str] = Field(
        default=None, 
        alias="product", 
        serialization_alias="Product", 
        description="Product identifier"
    )
    billing_id: Optional[str] = Field(
        default=None, 
        alias="billingID", 
        serialization_alias="BillingId", 
        description="Billing identifier"
    )
    provider_qa_contact: Optional[str] = Field(
        default=None, 
        alias="providerQaContact", 
        serialization_alias="ProviderQaContact", 
        description="Provider QA contact"
    )
    placing_id: Optional[int] = Field(
        default=None, 
        alias="placingId", 
        serialization_alias="PlacingId", 
        description="Placing identifier"
    )
    
  