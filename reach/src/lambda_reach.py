from typing import Any

from utils.config import ENV, settings_config
from utils.LoggerService import logger_service
from utils.schemas.lambdas import EventSchema
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker
from utils.token_service import ReachTokenService

from .reach_flow import flow


def lambda_handler(event: dict[str, Any], _: str) -> None:
    """
    Lambda function entry point for updating Reach metadata.

    Args:
        event: Lambda event containing wonderland sidecar ID
        context: Lambda context
    """
    logger_service.info("************ IZMA / Running Lambda Reach ************")
    logger_service.info("IZMA / Running Lambda Reach with args: %s", event)

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    project_name = "reach"
    settings_service.initialize(project_name)
    event_settings = settings_service.settings
    status_table_name = event_settings.get_status_table()
    sidecar_table = event_settings.configuration_rules.config.sidecar_table
    wonderland_delivery_status_mapping = (
        event_settings.configuration_rules.config.wonderland_delivery_status_mapping
    )

    token_service = ReachTokenService()
    table_name = f"{sidecar_table}-{ENV}"

    status_tracker = StatusTrackerSchema(project=project_name)
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    event_project = EventSchema(**event)
    dynamodb_stream = event_project.get_dynamodb_stream_record()

    global_variables = {
        "table_name": table_name,
        "dynamodb_stream": dynamodb_stream,
        "token": token_service.create_token(),
        "url": token_service.reach_secrets["reach_url"],
        "wonderland_delivery_status_mapping": wonderland_delivery_status_mapping,
    }

    try:
        process_status_tracker.mark_as_running(status_tracker.client_id)

        flow(event_project, global_variables)

        process_status_tracker.mark_as_completed(status_tracker.client_id)

        logger_service.info("IZMA / Lambda Reach Finished Successfully")

    except Exception as error:
        logger_service.error("IZMA / Lambda Reach Failed: %s", str(error))
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(status_tracker.client_id, error_message)
        raise error


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "eventID": "13b2cc594dac3fe189bf138703ea2795",
                    "eventName": "MODIFY",
                    "eventVersion": "1.1",
                    "eventSource": "aws:dynamodb",
                    "awsRegion": "us-west-2",
                    "dynamodb": {
                        "ApproximateCreationDateTime": 1748620013.0,
                        "Keys": {"sidecar_id": {"S": "b374b7b2-0321-499b-a8c3-f0653e2d1d51"}},
                        "NewImage": {
                            "reach_package_id": {"S": "10713750"},
                            "updated_at": {"S": "2025-05-30T10:46:53.623199"},
                            "created_at": {"S": "2025-05-30T10:46:53.623199"},
                            "wonderland_id": {"S": ""},
                            "details": {"S": "The file has been uploaded. Ingestion is in progress"},
                            "sidecar_id": {"S": "b374b7b2-0321-499b-a8c3-f0653e2d1d51"},
                            "deleted_at": {"S": ""},
                            "status": {"S": "wl_in_progress"},
                        },
                        "OldImage": {
                            "reach_package_id": {"S": "10713750"},
                            "updated_at": {"S": "2025-05-29T23:27:02.627707"},
                            "created_at": {"S": "2025-05-29T23:27:02.627707"},
                            "wonderland_id": {"S": ""},
                            "details": {"S": "The file has been uploaded. Ingestion is in progress"},
                            "sidecar_id": {"S": "b374b7b2-0321-499b-a8c3-f0653e2d1d51"},
                            "deleted_at": {"S": ""},
                            "status": {"S": "wl_in_progress"},
                        },
                        "SequenceNumber": "9393600000346538456177347",
                        "SizeBytes": 534,
                        "StreamViewType": "NEW_AND_OLD_IMAGES",
                    },
                }
            ]
        },
        "",
    )
