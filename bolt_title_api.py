import os
from typing import Optional, Dict, Any, List
import json

import requests

from utils.config import settings_config
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings_manager import SettingsManagerService


def titleapi_post_search_titles_feature(auth_bearer_token: str, movie_name: str) -> Optional[Dict[str, Any]]:
    """
    Perform the same request as the provided curl: searchTitles for a Feature by NAME.
    Returns the raw JSON response or None on error.
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: NAME, values: [ \""""
        + movie_name
        + """\" ]}, 
          { field: TYPE, values: [ "Feature" ] } 
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        }
      }
    }
  }
}"""
    )
    headers = {
        "Authorization": _ensure_bearer_prefix(auth_bearer_token),
        "Content-Type": "application/json",
        "User-Agent": "insomnia/11.1.0",
    }
    json_body = {"query": graphql_string, "variables": {"movie_name": movie_name}}
    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=headers,
        timeout=60,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(f"TitleAPI error (feature search): {this_err.get('message')}")
        return resp_json
    return None



def _ensure_bearer_prefix(token: str) -> str:
    """
    Ensure the Bolt bearer token has 'Bearer ' prefix.
    """
    token = token or ""
    if token and not token.startswith("Bearer "):
        return "Bearer " + token
    return token


def authz_get_token(client_id: str, client_secret: str, grant_type: str, scope: str) -> Dict[str, str]:
    """
    Retrieve an OAuth token from AuthZ suitable for Title API requests.
    Returns an Authorization header dict on success: {'Authorization': 'Bearer ...'}
    """
    params = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": grant_type,
        "scope": scope,
    }
    resp = requests.post(
        url="https://cp-auth-service.maestro.dmed.technology/v2/as/token.oauth2",
        params=params,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        return {"Authorization": f"{resp_json['token_type']} {resp_json['access_token']}"}
    resp.raise_for_status()
    return {}

def _get_authz_values_from_secrets(project_name: str = "bebanjo") -> Dict[str, str]:
    """
    Obtain AuthZ values (client_id, client_secret, grant_type, scope) using settings and AWS Secrets Manager.
    """
    try:
        settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
        dynamodb_settings = settings_service.initialize(project_name)
        config = dynamodb_settings.get_config()  # type: ignore[attr-defined]
        secrets = SecretsManagerUtils().get_secret_value(config.secret_key)  # type: ignore[attr-defined]
    except Exception:
        return {}

    def _pick(*keys: str) -> str:
        for k in keys:
            v = secrets.get(k)
            if v:
                return str(v)
        return ""

    return {
        "client_id": _pick("AUTHZ_CLIENT_ID", "authz_client_id", "auth_z_client_id", "client_id"),
        "client_secret": _pick("AUTHZ_CLIENT_SECRET", "authz_client_secret", "auth_z_client_secret", "client_secret"),
        "grant_type": _pick("AUTHZ_GRANT_TYPE", "authz_grant_type", "auth_z_grant_type", "grant_type") or "client_credentials",
        "scope": _pick("AUTHZ_SCOPE", "authz_scope", "auth_z_scope", "scope"),
    }


def _build_authz_headers_from_secrets(project_name: str = "bebanjo") -> Optional[Dict[str, str]]:
    """
    Build Authorization headers using AuthZ credentials retrieved from Secrets Manager.
    """
    vals = _get_authz_values_from_secrets(project_name)
    client_id = vals.get("client_id", "")
    client_secret = vals.get("client_secret", "")
    grant_type = vals.get("grant_type", "client_credentials")
    scope = vals.get("scope", "")
    if client_id and client_secret and scope:
        try:
            return authz_get_token(client_id, client_secret, grant_type, scope)
        except Exception:
            return None
    return None



def titleapi_run_search_for_episode(auth_token: Dict[str, str], series_name: str, season_num: str, episode_title: str) -> Optional[Dict[str, Any]]:
    """
    Query Title API for an Episode given series name, season, and episode title.
    Returns the raw JSON or None on error.
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: SERIES_NAME, values: [ \""""
        + series_name
        + """\" ] }, 
          { field: SEASON_NAME, values: [ \""""
        + season_num
        + """\" ] }, 
          { field: NAME, values: [ \""""
        + episode_title
        + """\" ] } 
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )
    json_body = {"query": graphql_string, "variables": {}}
    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
        timeout=60,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(f"TitleAPI error (episode search): {this_err.get('message')}")
        return resp_json
    return None


def titleapi_run_search_for_special(auth_token: Dict[str, str], series_name: str, season_num: str, episode_title: str) -> Optional[Dict[str, Any]]:
    """
    Query Title API for a Special by title.
    Returns the raw JSON or None on error.
    """
    graphql_string = (
        """query{
    searchTitles(input:
      {stringFilterFields:
        [ { field: TYPE, values: [ "Special"]},
          { field: NAME, values: [ \""""
        + episode_title
        + """\" ] }
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        episodeNumber,
        seasonNumber,
        seasonEpisodeNumber,
        firstAirRunningOrder,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        },
        parent {
          id,
          type,
          name,
          earliestReleaseDate,
          genres,
          keywords,
          synopses {
            synopsisShort
            synopsisMedium
            synopsisFull
          },
          parent {
            id,
            type,
            name,
            earliestReleaseDate,
            keywords,
            productGroupId,
            synopses {
              synopsisShort
              synopsisMedium
              synopsisFull
            }
          }
        }
      }
    }
  }
}"""
    )
    json_body = {"query": graphql_string, "variables": {}}
    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
        timeout=60,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if "errors" in resp_json:
            for this_err in resp_json["errors"]:
                print(f"TitleAPI error (special search): {this_err.get('message')}")
            return None
        return resp_json
    return None


def titleapi_run_search_for_movie(auth_token: Dict[str, str], movie_name: str, movie_year: str) -> Optional[Dict[str, Any]]:
    """
    Query Title API for a Feature (movie) by name.
    Tries to narrow down to exact matches and fallback heuristics similar to the original implementation.
    Returns the raw JSON (possibly filtered) or None on error.
    """
    graphql_string = (
        """query{
    searchTitles(input: 
      {stringFilterFields: 
        [ { field: NAME, values: [ \""""
        + movie_name
        + """\" ]}, 
          { field: TYPE, values: [ "Feature" ] } 
        ]
      }
    ) {
    totalCount,
    edges {
      cursor,
      node {
        id,
        type,
        name,
        earliestReleaseDate,
        genres,
        productId,
        productNumber,
        productGroupId,
        productionNumber,
        keywords,
        synopses {
          synopsisShort
          synopsisMedium
          synopsisFull
        }
      }
    }
  }
}"""
    )
    json_body = {"query": graphql_string, "variables": {}}
    resp = requests.post(
        url="https://title-api.maestro.dmed.technology/graphql",
        json=json_body,
        headers=auth_token,
        timeout=60,
    )
    if resp.status_code != requests.codes.ok:
        return None

    resp_json = resp.json()
    if "errors" in resp_json:
        for this_err in resp_json["errors"]:
            print(f"TitleAPI error (movie search): {this_err.get('message')}")
        return resp_json

    # Attempt to return only exact matches if possible
    edges: List[Dict[str, Any]] = resp_json.get("data", {}).get("searchTitles", {}).get("edges", [])
    edge_count = len(edges)
    exact_matches = sum(1 for e in edges if e.get("node", {}).get("name", "") == movie_name)

    if exact_matches == 1 and edge_count >= 1:
        edges = [x for x in edges if x.get("node", {}).get("name", "") == movie_name]
        resp_json["data"]["searchTitles"]["edges"] = edges
        return resp_json

    # Handle "The ", "A ", "An " leading articles
    movie_name_converted = movie_name
    if movie_name.startswith("The "):
        movie_name_converted = movie_name[4:] + ", The"
    if movie_name.startswith("A "):
        movie_name_converted = movie_name[2:] + ", A"
    if movie_name.startswith("An "):
        movie_name_converted = movie_name[3:] + ", An"

    exact_matches_conv = sum(1 for e in edges if e.get("node", {}).get("name", "") == movie_name_converted)
    if exact_matches_conv == 1 and edge_count >= 1:
        edges = [x for x in edges if x.get("node", {}).get("name", "") == movie_name_converted]
        resp_json["data"]["searchTitles"]["edges"] = edges
        return resp_json

    # As a final heuristic, if a year is provided, try to filter by earliestReleaseDate year
    if movie_year and edge_count > 1:
        filtered = []
        for e in edges:
            dt = e.get("node", {}).get("earliestReleaseDate", "")
            if isinstance(dt, str) and len(dt) >= 4:
                if dt[:4] == movie_year:
                    filtered.append(e)
        if filtered:
            resp_json["data"]["searchTitles"]["edges"] = filtered
            return resp_json

    return resp_json


def bolt_get_series_from_group_id(auth_bearer_token: str, radar_group_id: str) -> Any:
    """
    Get Bolt series data given a Radar Group ID.
    """
    params = {"radarGroupId": radar_group_id}
    auth_token = {"Authorization": _ensure_bearer_prefix(auth_bearer_token)}
    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/series",
        params=params,
        headers=auth_token,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if isinstance(resp_json, list) and len(resp_json) == 1:
            return resp_json[0]
        return resp_json
    resp.raise_for_status()


def bolt_get_movie_from_group_id(auth_bearer_token: str, radar_group_id: str) -> Any:
    """
    Get Bolt feature (movie) data given a Radar Group ID.
    """
    params = {"radarGroupId": radar_group_id}
    auth_token = {"Authorization": _ensure_bearer_prefix(auth_bearer_token)}
    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/feature",
        params=params,
        headers=auth_token,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        if isinstance(resp_json, list) and len(resp_json) == 1:
            return resp_json[0]
        return resp_json
    resp.raise_for_status()


def bolt_get_season_from_series_id(auth_bearer_token: str, bolt_series_id: str, season_num: str) -> Any:
    """
    Get a specific Bolt season data given a Series ID and season number.
    """
    params = {"seriesId": bolt_series_id}
    auth_token = {"Authorization": _ensure_bearer_prefix(auth_bearer_token)}
    resp = requests.get(
        url="https://bolt.studio.disney.com/wam-api/season",
        params=params,
        headers=auth_token,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
        for this_season in resp_json:
            try:
                this_season_num = this_season["seasonNumber"]
            except KeyError:
                continue
            if str(this_season_num) == str(season_num):
                return this_season
        return ""
    resp.raise_for_status()


def bolt_get_cast_from_season_id(auth_bearer_token: str, bolt_season_id: str) -> Any:
    """
    Get cast for a Bolt Season ID.
    """
    auth_token = {"Authorization": _ensure_bearer_prefix(auth_bearer_token)}
    resp = requests.get(
        url=f"https://bolt.studio.disney.com/wam-api/season/{bolt_season_id}/cast/original/expanded",
        headers=auth_token,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        return resp.json()
    resp.raise_for_status()


def bolt_get_cast_from_feature_id(auth_bearer_token: str, bolt_feature_id: str) -> List[Dict[str, Any]]:
    """
    Get (first 10) cast entries for a Bolt Feature ID.
    """
    auth_token = {"Authorization": _ensure_bearer_prefix(auth_bearer_token)}
    resp = requests.get(
        url=f"https://bolt.studio.disney.com/wam-api/feature/{bolt_feature_id}/cast/original/expanded",
        headers=auth_token,
        timeout=30,
    )
    if resp.status_code == requests.codes.ok:
        resp_json = resp.json()
    else:
        resp.raise_for_status()
        resp_json = []

    # Only return first 10 actors based on 'order'
    cast_list_short = []
    for this_actor in resp_json:
        listing_num_str = this_actor.get("member", {}).get("order")
        if listing_num_str is not None:
            try:
                if int(listing_num_str) <= 10:
                    cast_list_short.append(this_actor)
            except Exception:
                continue
        if len(cast_list_short) > 9:
            break

    return cast_list_short


def search_title_api_by_type(
    title_name: str,
    program_type: str,
    season_num: str = "",
    episode_title: str = "",
    movie_year: str = "",
    auth_headers: Optional[Dict[str, str]] = None,
    client_id: Optional[str] = None,
    client_secret: Optional[str] = None,
    grant_type: Optional[str] = None,
    scope: Optional[str] = None,
) -> Optional[Dict[str, Any]]:
    """
    Convenience function to query Title API given a title and type.

    Args:
        title_name: Series or Movie title to search.
        program_type: 'series' | 'special' | 'movie'
        season_num: Required for series episode searches.
        episode_title: Episode title for series/special searches.
        movie_year: Optional year filter for movies.
        auth_headers: Prebuilt Authorization header dict. If not provided, it will be created using AuthZ credentials.
        client_id/client_secret/grant_type/scope: AuthZ credentials; if omitted, env variables are used:
            AUTHZ_CLIENT_ID, AUTHZ_CLIENT_SECRET, AUTHZ_GRANT_TYPE, AUTHZ_SCOPE

    Returns:
        Raw JSON response from Title API (dict) or None on error.
    """
    if auth_headers is None:
        # Try to obtain credentials from Secrets Manager-backed settings first
        if not client_id or not client_secret or not grant_type or not scope:
            secret_vals = _get_authz_values_from_secrets()
            client_id = client_id or secret_vals.get("client_id", "")
            client_secret = client_secret or secret_vals.get("client_secret", "")
            grant_type = grant_type or secret_vals.get("grant_type", "client_credentials")
            scope = scope or secret_vals.get("scope", "")

        # Fallback to environment variables if anything is still missing
        client_id = client_id or os.getenv("AUTHZ_CLIENT_ID", "")
        client_secret = client_secret or os.getenv("AUTHZ_CLIENT_SECRET", "")
        grant_type = grant_type or os.getenv("AUTHZ_GRANT_TYPE", "client_credentials")
        scope = scope or os.getenv("AUTHZ_SCOPE", "")
        if not client_id or not client_secret or not grant_type or not scope:
            print("Missing AuthZ credentials; provide auth_headers or set env vars.")
            return None
        try:
            auth_headers = authz_get_token(client_id, client_secret, grant_type, scope)
        except Exception as e:
            print(f"Failed to obtain AuthZ token: {e}")
            return None

    pt = (program_type or "").strip().lower()
    if pt == "series":
        if not season_num or not episode_title:
            print("For 'series' searches, season_num and episode_title are required.")
            return None
        return titleapi_run_search_for_episode(auth_headers, title_name, season_num, episode_title)
    if pt == "special":
        if not episode_title:
            print("For 'special' searches, episode_title is required.")
            return None
        return titleapi_run_search_for_special(auth_headers, title_name, season_num, episode_title)
    if pt == "movie":
        return titleapi_run_search_for_movie(auth_headers, title_name, movie_year)

    print(f"Unsupported program_type: {program_type}. Use 'series', 'special', or 'movie'.")
    return None


def debug_auth_credentials():
    """
    Debug function to check authentication credentials and token generation.
    """
    print("=== DEBUGGING AUTHENTICATION ===")

    # Check secrets from AWS
    print("1. Checking secrets from AWS Secrets Manager...")
    secret_vals = _get_authz_values_from_secrets()
    print(f"   - client_id: {'✓' if secret_vals.get('client_id') else '✗'}")
    print(f"   - client_secret: {'✓' if secret_vals.get('client_secret') else '✗'}")
    print(f"   - grant_type: {secret_vals.get('grant_type', 'NOT SET')}")
    print(f"   - scope: {secret_vals.get('scope', 'NOT SET')}")

    # Check environment variables
    print("\n2. Checking environment variables...")
    env_client_id = os.getenv("AUTHZ_CLIENT_ID", "")
    env_client_secret = os.getenv("AUTHZ_CLIENT_SECRET", "")
    env_grant_type = os.getenv("AUTHZ_GRANT_TYPE", "client_credentials")
    env_scope = os.getenv("AUTHZ_SCOPE", "")

    print(f"   - AUTHZ_CLIENT_ID: {'✓' if env_client_id else '✗'}")
    print(f"   - AUTHZ_CLIENT_SECRET: {'✓' if env_client_secret else '✗'}")
    print(f"   - AUTHZ_GRANT_TYPE: {env_grant_type}")
    print(f"   - AUTHZ_SCOPE: {env_scope or 'NOT SET'}")

    # Try to get token
    print("\n3. Testing token generation...")
    try:
        auth_headers = _build_authz_headers_from_secrets()
        if auth_headers:
            print("   ✓ Successfully obtained token from secrets")
            token = auth_headers.get("Authorization", "")
            print(f"   Token preview: {token[:50]}..." if len(token) > 50 else f"   Token: {token}")
        else:
            print("   ✗ Failed to get token from secrets, trying environment variables...")
            if env_client_id and env_client_secret and env_scope:
                auth_headers = authz_get_token(env_client_id, env_client_secret, env_grant_type, env_scope)
                print("   ✓ Successfully obtained token from environment variables")
                token = auth_headers.get("Authorization", "")
                print(f"   Token preview: {token[:50]}..." if len(token) > 50 else f"   Token: {token}")
            else:
                print("   ✗ Insufficient credentials in environment variables")
    except Exception as e:
        print(f"   ✗ Error during token generation: {e}")

    print("=== END DEBUGGING ===\n")


def main():
    """
    Main function to execute a search for the movie "Cast Away".
    """
    movie_name = "Cast Away"

    # Debug authentication first
    debug_auth_credentials()

    # Try to get auth token from secrets first
    auth_headers = _build_authz_headers_from_secrets()

    if auth_headers is None:
        # Fallback to environment variables
        client_id = os.getenv("AUTHZ_CLIENT_ID", "")
        client_secret = os.getenv("AUTHZ_CLIENT_SECRET", "")
        grant_type = os.getenv("AUTHZ_GRANT_TYPE", "client_credentials")
        scope = os.getenv("AUTHZ_SCOPE", "")

        if not client_id or not client_secret or not scope:
            print("Error: Missing authentication credentials.")
            print("Please set the following environment variables:")
            print("- AUTHZ_CLIENT_ID")
            print("- AUTHZ_CLIENT_SECRET")
            print("- AUTHZ_SCOPE (should be: dtci-theforce-title-general-only)")
            print("- AUTHZ_GRANT_TYPE (optional, defaults to 'client_credentials')")
            return

        try:
            auth_headers = authz_get_token(client_id, client_secret, grant_type, scope)
        except Exception as e:
            print(f"Error obtaining authentication token: {e}")
            return

    # Extract the bearer token from auth headers
    auth_token = auth_headers.get("Authorization", "")
    if not auth_token:
        print("Error: Failed to obtain valid authentication token.")
        return

    print(f"Searching for movie: {movie_name}")
    print("-" * 50)

    # Execute the search using titleapi_post_search_titles_feature
    result = titleapi_post_search_titles_feature(auth_token, movie_name)

    if result is None:
        print("Error: Search request failed.")
        return

    # Pretty print the results
    print("Search Results:")
    print(json.dumps(result, indent=2))

    # Extract and display key information if available
    if "data" in result and "searchTitles" in result["data"]:
        search_data = result["data"]["searchTitles"]
        total_count = search_data.get("totalCount", 0)
        edges = search_data.get("edges", [])

        print(f"\nTotal results found: {total_count}")

        if edges:
            print("\nTitle Details:")
            for i, edge in enumerate(edges, 1):
                node = edge.get("node", {})
                print(f"\n{i}. {node.get('name', 'N/A')}")
                print(f"   ID: {node.get('id', 'N/A')}")
                print(f"   Type: {node.get('type', 'N/A')}")
                print(f"   Release Date: {node.get('earliestReleaseDate', 'N/A')}")
                print(f"   Genres: {', '.join(node.get('genres', []))}")
                print(f"   Product ID: {node.get('productId', 'N/A')}")

                # Display synopsis if available
                synopses = node.get('synopses', {})
                if synopses:
                    short_synopsis = synopses.get('synopsisShort', '')
                    if short_synopsis:
                        print(f"   Synopsis: {short_synopsis}")
        else:
            print("No titles found matching the search criteria.")
    else:
        print("Unexpected response format.")


if __name__ == "__main__":
    main()
