from typing import Any

from utils.extract import extract
from utils.flow_manager import FlowManager
from utils.schemas.lambdas import EventSchema
from utils.task_handlers import (
    TaskDeserializeData,
    TaskExtractData,
    TaskLoadToReach,
    TaskParseNormalSeries,
)


def flow(event: EventSchema, global_variables: dict[str, Any]) -> Any:
    """
    Lambda function moving files between buckets.
    Takes a JSON file from S3 bucket, and saves it as YAML.

    Args:
        event_settings (Dict): AWS Lambda event containing S3 trigger information
        global_variables (Optional[Dict]): AWS Lambda context
    """

    global_variables["connector"] = extract

    _flow = FlowManager(
        name="bebanjo",
        tasks=[
            TaskExtractData(),
            TaskDeserializeData(),
            # TODO: Disabled temporarily due some requirements need definition
            # TaskMapJsonKeys(),
            TaskParseNormalSeries(),
            TaskLoadToReach()
        ],
        event=event,
        global_variables=global_variables,
        successful_message=f"Package Successfully created into reach from file {event.get_file_name_from_s3()}",
    )
    return _flow()
