from typing import Any

from utils.config import settings_config
from utils.LoggerService import logger_service
from utils.RetryHandler import <PERSON><PERSON><PERSON>and<PERSON>
from utils.schemas.dynamodb.bebanjo import BebanjoConfig
from utils.schemas.lambdas import EventSchema
from utils.schemas.status_tracker_schema import StatusTrackerSchema
from utils.SecretsManagerUtils import SecretsManagerUtils
from utils.settings_manager import SettingsManagerService
from utils.status_tracker import ProcessStatusTracker

from .bebanjo_flow import flow


def lambda_handler(event: dict[str, Any], _: str) -> None:
    """Entry point for lambda function."""
    logger_service.info("************ IZMA / lambda bebanjo started ************")
    logger_service.info("IZMA / lambda bebanjo with args: \n %s", event)

    settings_service = SettingsManagerService(settings_config.TACDEV_EVENT_CONFIG)
    project_name = "bebanjo"
    dynamodb_settings = settings_service.initialize(project_name)
    config: BebanjoConfig = dynamodb_settings.get_config()  # type: ignore
    status_table_name = dynamodb_settings.get_status_table()

    event_object = EventSchema(**event)  # type: ignore
    file_name_from_s3 = event_object.get_file_name_from_s3()
    bucket_name = event_object.get_bucket_name()

    status_tracker = StatusTrackerSchema(
        client_id=file_name_from_s3, landing_bucket=bucket_name, project=project_name
    )
    process_status_tracker = ProcessStatusTracker(status_table_name)
    process_status_tracker.create_initial_status(create_item=status_tracker)

    secrets = SecretsManagerUtils()
    bebanjo_secrets = secrets.get_secret_value(config.secret_key)
    global_variables = {
        "landing_bucket": bucket_name,
        "destination_bucket": config.destination_bucket,
        "file_name": file_name_from_s3,
        "secrets": bebanjo_secrets,
        "mapping_rules": dynamodb_settings.configuration_rules.rules.get("mapping_rules_keys", []),
    }

    try:
        process_status_tracker.mark_as_running(file_name_from_s3)

        flow(event_object, global_variables)

        process_status_tracker.mark_as_completed(file_name_from_s3)

    except BaseException as error:
        logger_service.error(
            "IZMA ERROR: %s, event_project: %s",
            error,
            dynamodb_settings.event_project_name,
        )
        error_message = f"{error}"
        process_status_tracker.mark_as_failed(file_name_from_s3, error_message)

        retry_handler = RetryHandler(dynamodb_settings.retries)
        retry_handler.execute(flow, dynamodb_settings, global_variables)

    logger_service.info("IZMA / lambda bebanjo finished")


if __name__ == "__main__":
    lambda_handler(
        {
            "Records": [
                {
                    "s3": {
                        "object": {
                            # Serie Ladybug
                            # "key": "bebanjo_sample_last.json",
                            # Movie
                            # "key": "preprod/ff/TIER 1 VOD ATT FRFM SD LF D4 Encore-mass0000000013626848-20250219T225506891.json",
                            # Serie Shark Tank
                            "key": "bebanjo_shark_tank.json",
                        },
                        "bucket": {"name": "reachengine-nldschedules-sbx"},
                    }
                }
            ]
        },
        "",
    )
